using System;
using System.Collections.Generic;
using System.Linq;
using Avalonia;
using Avalonia.Controls;
using Avalonia.Media;
using Avalonia.Controls.Shapes;
using Avalonia.Input;
using System.Collections.ObjectModel;

namespace TartanLogApp.CurveControl
{
    /// <summary>
    /// Avalonia版本的Gamma曲线显示控件
    /// </summary>
    public class GammaCurveControl : UserControl
    {
        #region 属性定义

        // 主要画布
        private Canvas _mainCanvas;
        private Canvas _headerCanvas;
        private Canvas _curveCanvas;
        private Canvas _rulerCanvas;
        private Canvas _gridCanvas;
        private Canvas _imagingCanvas;
        private Canvas _xAxisCanvas; // X轴画布

        // 数据相关
        public ObservableCollection<GammaSeries> Series { get; set; }
        public GammaImagingData ImagingData { get; set; }

        // 显示参数
        public double MinDepth { get; set; } = 0;
        public double MaxDepth { get; set; } = 100;
        public double DepthRange { get; set; } = 120; // 显示范围
        public double GridInterval { get; set; } = 20; // 网格间隔

        // X轴参数（Gamma值）
        public double MinGammaDisplayValue { get; set; } = 0;
        public double MaxGammaDisplayValue { get; set; } = 200;
        public string GammaUnit { get; set; } = "API"; // Gamma射线单位
        public double GammaGridInterval { get; set; } = 50; // Gamma值网格间隔

        // 成像参数
        public bool IsImagingEnabled { get; set; } = false;
        public int SectorCount { get; set; } = 2; // 2扇区或4扇区
        public double MinGammaValue { get; set; } = 0;
        public double MaxGammaValue { get; set; } = 200;

        // 颜色配置
        private readonly List<IBrush> _seriesColors = new List<IBrush>
        {
            new SolidColorBrush(Color.FromRgb(45, 137, 239)), // 蓝色
            new SolidColorBrush(Color.FromRgb(238, 17, 17)),   // 红色
            new SolidColorBrush(Color.FromRgb(255, 196, 13)),  // 黄色
            new SolidColorBrush(Color.FromRgb(0, 171, 169)),   // 青色
            new SolidColorBrush(Color.FromRgb(76, 175, 80)),   // 绿色
            new SolidColorBrush(Color.FromRgb(156, 39, 176))   // 紫色
        };

        #endregion

        #region 构造函数

        public GammaCurveControl()
        {
            InitializeComponent();
            InitializeData();
        }

        private void InitializeComponent()
        {
            Background = new SolidColorBrush(Colors.White);
            ClipToBounds = true;

            // 创建主画布
            _mainCanvas = new Canvas();
            Content = _mainCanvas;

            // 创建子画布
            _headerCanvas = new Canvas { Background = new SolidColorBrush(Color.FromRgb(248, 249, 250)) };
            _gridCanvas = new Canvas { Background = new SolidColorBrush(Colors.White) };
            _curveCanvas = new Canvas { Background = Brushes.Transparent };
            _rulerCanvas = new Canvas { Background = Brushes.Transparent };
            _imagingCanvas = new Canvas { Background = Brushes.Transparent };
            _xAxisCanvas = new Canvas { Background = new SolidColorBrush(Color.FromRgb(248, 249, 250)) };

            // 添加到主画布
            _mainCanvas.Children.Add(_gridCanvas);
            _mainCanvas.Children.Add(_imagingCanvas);
            _mainCanvas.Children.Add(_curveCanvas);
            _mainCanvas.Children.Add(_rulerCanvas);
            _mainCanvas.Children.Add(_headerCanvas);
            _mainCanvas.Children.Add(_xAxisCanvas);

            // 绑定事件
            SizeChanged += OnSizeChanged;
            PointerMoved += OnPointerMoved;
            PointerWheelChanged += OnPointerWheelChanged;
        }

        private void InitializeData()
        {
            Series = new ObservableCollection<GammaSeries>();
            ImagingData = new GammaImagingData();
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 添加数据系列
        /// </summary>
        public void AddSeries(string name, IBrush color = null)
        {
            var series = new GammaSeries
            {
                Name = name,
                Color = color ?? _seriesColors[Series.Count % _seriesColors.Count],
                StrokeThickness = 2,
                Points = new List<GammaDataPoint>()
            };

            Series.Add(series);
        }

        /// <summary>
        /// 添加数据点
        /// </summary>
        public void AddDataPoint(string seriesName, double depth, double value)
        {
            var series = Series.FirstOrDefault(s => s.Name == seriesName);
            if (series != null)
            {
                series.Points.Add(new GammaDataPoint { Depth = depth, Value = value });

                // 更新深度范围
                UpdateDepthRange();
            }
        }

        /// <summary>
        /// 清空所有数据
        /// </summary>
        public void ClearData()
        {
            foreach (var series in Series)
            {
                series.Points.Clear();
            }
            ImagingData.Clear();
            Refresh();
        }

        /// <summary>
        /// 设置Gamma成像数据
        /// </summary>
        public void SetImagingData(List<GammaImagingPoint> imagingPoints, int sectorCount)
        {
            SectorCount = sectorCount;
            ImagingData.Points = imagingPoints;
            IsImagingEnabled = imagingPoints.Count > 0;
            Refresh();
        }

        /// <summary>
        /// 刷新显示
        /// </summary>
        public void Refresh()
        {
            if (Bounds.Width <= 0 || Bounds.Height <= 0) return;

            ClearCanvas();
            UpdateCanvasLayout();
            DrawGrid();
            DrawCurves();
            DrawHeader();
            DrawRuler();
            DrawXAxis();

            if (IsImagingEnabled)
            {
                DrawImaging();
            }
        }

        #endregion

        #region 私有方法 - 事件处理

        private void OnSizeChanged(object sender, SizeChangedEventArgs e)
        {
            UpdateCanvasLayout();
            Refresh();
        }

        private void OnPointerMoved(object sender, PointerEventArgs e)
        {
            // 实现鼠标悬停显示数据点信息
            var position = e.GetPosition(_curveCanvas);
            ShowDataAtPosition(position);
        }

        private void OnPointerWheelChanged(object sender, PointerWheelEventArgs e)
        {
            // 实现鼠标滚轮缩放
            var delta = e.Delta.Y;
            if (delta > 0)
            {
                ZoomIn();
            }
            else
            {
                ZoomOut();
            }
        }

        private void UpdateCanvasLayout()
        {
            if (Bounds.Width <= 0 || Bounds.Height <= 0) return;

            double width = Bounds.Width;
            double height = Bounds.Height;

            // 布局参数
            double headerHeight = 60;
            double rulerWidth = 80;
            double xAxisHeight = 50; // X轴区域高度
            double imagingWidth = IsImagingEnabled ? 100 : 0;

            // 设置各画布位置和大小
            Canvas.SetLeft(_headerCanvas, rulerWidth);
            Canvas.SetTop(_headerCanvas, 0);
            _headerCanvas.Width = width - rulerWidth - imagingWidth;
            _headerCanvas.Height = headerHeight;

            Canvas.SetLeft(_gridCanvas, rulerWidth);
            Canvas.SetTop(_gridCanvas, headerHeight);
            _gridCanvas.Width = width - rulerWidth - imagingWidth;
            _gridCanvas.Height = height - headerHeight - xAxisHeight;

            Canvas.SetLeft(_curveCanvas, rulerWidth);
            Canvas.SetTop(_curveCanvas, headerHeight);
            _curveCanvas.Width = width - rulerWidth - imagingWidth;
            _curveCanvas.Height = height - headerHeight - xAxisHeight;

            Canvas.SetLeft(_rulerCanvas, 0);
            Canvas.SetTop(_rulerCanvas, headerHeight);
            _rulerCanvas.Width = rulerWidth;
            _rulerCanvas.Height = height - headerHeight - xAxisHeight;

            // X轴画布
            Canvas.SetLeft(_xAxisCanvas, rulerWidth);
            Canvas.SetTop(_xAxisCanvas, height - xAxisHeight);
            _xAxisCanvas.Width = width - rulerWidth - imagingWidth;
            _xAxisCanvas.Height = xAxisHeight;

            if (IsImagingEnabled)
            {
                Canvas.SetLeft(_imagingCanvas, width - imagingWidth);
                Canvas.SetTop(_imagingCanvas, headerHeight);
                _imagingCanvas.Width = imagingWidth;
                _imagingCanvas.Height = height - headerHeight - xAxisHeight;
            }
        }

        private void UpdateDepthRange()
        {
            if (!Series.Any() || !Series.Any(s => s.Points.Any())) return;

            var allPoints = Series.SelectMany(s => s.Points);
            if (allPoints.Any())
            {
                MaxDepth = allPoints.Max(p => p.Depth);
                MinDepth = Math.Max(0, MaxDepth - DepthRange);
            }
        }

        private void UpdateGammaDisplayRange()
        {
            if (!Series.Any() || !Series.Any(s => s.Points.Any()))
            {
                // 使用默认范围
                MinGammaDisplayValue = 0;
                MaxGammaDisplayValue = 200;
                return;
            }

            var allPoints = Series.SelectMany(s => s.Points);
            if (allPoints.Any())
            {
                double minValue = allPoints.Min(p => p.Value);
                double maxValue = allPoints.Max(p => p.Value);

                // 添加10%的边距
                double margin = (maxValue - minValue) * 0.1;
                MinGammaDisplayValue = Math.Max(0, minValue - margin);
                MaxGammaDisplayValue = maxValue + margin;

                // 调整网格间隔
                double range = MaxGammaDisplayValue - MinGammaDisplayValue;
                if (range <= 50)
                    GammaGridInterval = 10;
                else if (range <= 200)
                    GammaGridInterval = 25;
                else if (range <= 500)
                    GammaGridInterval = 50;
                else
                    GammaGridInterval = 100;
            }
        }

        private void ClearCanvas()
        {
            _headerCanvas.Children.Clear();
            _gridCanvas.Children.Clear();
            _curveCanvas.Children.Clear();
            _rulerCanvas.Children.Clear();
            _imagingCanvas.Children.Clear();
            _xAxisCanvas.Children.Clear();
        }

        #endregion

        #region 绘制方法

        private void DrawGrid()
        {
            if (_gridCanvas.Width <= 0 || _gridCanvas.Height <= 0) return;

            double width = _gridCanvas.Width;
            double height = _gridCanvas.Height;

            // 绘制背景
            var background = new Rectangle
            {
                Width = width,
                Height = height,
                Fill = new SolidColorBrush(Colors.White),
                Stroke = new SolidColorBrush(Color.FromRgb(229, 231, 235)),
                StrokeThickness = 1
            };
            _gridCanvas.Children.Add(background);

            // 绘制水平网格线（深度）
            double depthStep = height / (DepthRange / GridInterval);
            for (int i = 0; i <= (DepthRange / GridInterval); i++)
            {
                double y = i * depthStep;
                var line = new Line
                {
                    StartPoint = new Point(0, y),
                    EndPoint = new Point(width, y),
                    Stroke = new SolidColorBrush(Color.FromRgb(243, 244, 246)),
                    StrokeThickness = 1
                };
                _gridCanvas.Children.Add(line);
            }

            // 绘制垂直网格线（对应X轴Gamma值刻度）
            UpdateGammaDisplayRange();
            double gammaRange = MaxGammaDisplayValue - MinGammaDisplayValue;
            if (gammaRange > 0)
            {
                int tickCount = (int)(gammaRange / GammaGridInterval);
                double tickStep = width / tickCount;

                for (int i = 0; i <= tickCount; i++)
                {
                    double x = i * tickStep;
                    var line = new Line
                    {
                        StartPoint = new Point(x, 0),
                        EndPoint = new Point(x, height),
                        Stroke = new SolidColorBrush(Color.FromRgb(243, 244, 246)),
                        StrokeThickness = 1
                    };
                    _gridCanvas.Children.Add(line);
                }
            }
        }

        private void DrawHeader()
        {
            if (_headerCanvas.Width <= 0 || _headerCanvas.Height <= 0) return;

            _headerCanvas.Children.Clear();

            double x = 10;
            double y = 10;

            // 绘制图例
            foreach (var series in Series.Where(s => s.IsVisible))
            {
                // 颜色块
                var colorRect = new Rectangle
                {
                    Width = 20,
                    Height = 12,
                    Fill = series.Color,
                    Stroke = new SolidColorBrush(Colors.Gray),
                    StrokeThickness = 1
                };
                Canvas.SetLeft(colorRect, x);
                Canvas.SetTop(colorRect, y);
                _headerCanvas.Children.Add(colorRect);

                // 标签
                var label = new TextBlock
                {
                    Text = series.Name,
                    FontSize = 12,
                    Foreground = new SolidColorBrush(Color.FromRgb(55, 65, 81))
                };
                Canvas.SetLeft(label, x + 25);
                Canvas.SetTop(label, y - 2);
                _headerCanvas.Children.Add(label);

                x += series.Name.Length * 8 + 50; // 动态调整间距

                if (x > _headerCanvas.Width - 100)
                {
                    x = 10;
                    y += 25;
                }
            }
        }

        private void DrawRuler()
        {
            if (_rulerCanvas.Width <= 0 || _rulerCanvas.Height <= 0) return;

            _rulerCanvas.Children.Clear();

            double height = _rulerCanvas.Height;
            double depthStep = height / (DepthRange / GridInterval);

            // 绘制深度刻度
            for (int i = 0; i <= (DepthRange / GridInterval); i++)
            {
                double depth = MinDepth + i * GridInterval;
                double y = i * depthStep;

                // 刻度线
                var tickLine = new Line
                {
                    StartPoint = new Point(_rulerCanvas.Width - 10, y),
                    EndPoint = new Point(_rulerCanvas.Width, y),
                    Stroke = new SolidColorBrush(Colors.Black),
                    StrokeThickness = 1
                };
                _rulerCanvas.Children.Add(tickLine);

                // 深度标签
                var depthLabel = new TextBlock
                {
                    Text = depth.ToString("F0"),
                    FontSize = 10,
                    Foreground = new SolidColorBrush(Color.FromRgb(55, 65, 81)),
                    TextAlignment = TextAlignment.Right
                };
                Canvas.SetLeft(depthLabel, _rulerCanvas.Width - 45);
                Canvas.SetTop(depthLabel, y - 7);
                _rulerCanvas.Children.Add(depthLabel);
            }

            // 深度单位标签
            var unitLabel = new TextBlock
            {
                Text = "深度(m)",
                FontSize = 12,
                FontWeight = FontWeight.Bold,
                Foreground = new SolidColorBrush(Color.FromRgb(55, 65, 81))
            };
            Canvas.SetLeft(unitLabel, 10);
            Canvas.SetTop(unitLabel, height / 2 - 10);
            _rulerCanvas.Children.Add(unitLabel);
        }

        private void DrawXAxis()
        {
            if (_xAxisCanvas.Width <= 0 || _xAxisCanvas.Height <= 0) return;

            _xAxisCanvas.Children.Clear();

            double width = _xAxisCanvas.Width;
            double height = _xAxisCanvas.Height;

            // 绘制X轴主线
            var axisLine = new Line
            {
                StartPoint = new Point(0, 0),
                EndPoint = new Point(width, 0),
                Stroke = new SolidColorBrush(Colors.Black),
                StrokeThickness = 2
            };
            _xAxisCanvas.Children.Add(axisLine);

            // 动态计算X轴范围
            UpdateGammaDisplayRange();

            // 绘制X轴刻度和标签
            double gammaRange = MaxGammaDisplayValue - MinGammaDisplayValue;
            if (gammaRange <= 0) return;

            int tickCount = (int)(gammaRange / GammaGridInterval);
            double tickStep = width / tickCount;

            for (int i = 0; i <= tickCount; i++)
            {
                double gammaValue = MinGammaDisplayValue + i * GammaGridInterval;
                double x = i * tickStep;

                // 刻度线
                var tickLine = new Line
                {
                    StartPoint = new Point(x, 0),
                    EndPoint = new Point(x, 10),
                    Stroke = new SolidColorBrush(Colors.Black),
                    StrokeThickness = 1
                };
                _xAxisCanvas.Children.Add(tickLine);

                // 刻度标签
                var tickLabel = new TextBlock
                {
                    Text = gammaValue.ToString("F0"),
                    FontSize = 10,
                    Foreground = new SolidColorBrush(Color.FromRgb(55, 65, 81)),
                    TextAlignment = TextAlignment.Center
                };
                Canvas.SetLeft(tickLabel, x - 15);
                Canvas.SetTop(tickLabel, 15);
                _xAxisCanvas.Children.Add(tickLabel);
            }

            // X轴标题
            var axisTitle = new TextBlock
            {
                Text = $"Gamma射线 ({GammaUnit})",
                FontSize = 12,
                FontWeight = FontWeight.Bold,
                Foreground = new SolidColorBrush(Color.FromRgb(55, 65, 81)),
                TextAlignment = TextAlignment.Center
            };
            Canvas.SetLeft(axisTitle, width / 2 - 50);
            Canvas.SetTop(axisTitle, height - 20);
            _xAxisCanvas.Children.Add(axisTitle);
        }

        private void DrawCurves()
        {
            if (_curveCanvas.Width <= 0 || _curveCanvas.Height <= 0) return;

            _curveCanvas.Children.Clear();

            double width = _curveCanvas.Width;
            double height = _curveCanvas.Height;

            foreach (var series in Series.Where(s => s.IsVisible && s.Points.Any()))
            {
                DrawSingleCurve(series, width, height);
            }
        }

        private void DrawSingleCurve(GammaSeries series, double width, double height)
        {
            var visiblePoints = series.Points
                .Where(p => p.Depth >= MinDepth && p.Depth <= MaxDepth)
                .OrderBy(p => p.Depth)
                .ToList();

            if (visiblePoints.Count < 2) return;

            // 使用统一的Gamma值范围
            double gammaRange = MaxGammaDisplayValue - MinGammaDisplayValue;
            if (gammaRange <= 0) return;

            // 创建路径
            var pathGeometry = new PathGeometry();
            var pathFigure = new PathFigure();

            // 起始点
            var firstPoint = visiblePoints.First();
            double startX = (firstPoint.Value - MinGammaDisplayValue) / gammaRange * width;
            double startY = (MaxDepth - firstPoint.Depth) / DepthRange * height;
            pathFigure.StartPoint = new Point(startX, startY);

            // 添加线段
            foreach (var point in visiblePoints.Skip(1))
            {
                double x = (point.Value - MinGammaDisplayValue) / gammaRange * width;
                double y = (MaxDepth - point.Depth) / DepthRange * height;

                pathFigure.Segments.Add(new LineSegment { Point = new Point(x, y) });
            }

            pathGeometry.Figures.Add(pathFigure);

            // 创建路径控件
            var path = new Path
            {
                Data = pathGeometry,
                Stroke = series.Color,
                StrokeThickness = series.StrokeThickness,
                Fill = null
            };

            _curveCanvas.Children.Add(path);
        }

        #endregion

        #region Gamma成像绘制

        private void DrawImaging()
        {
            if (!IsImagingEnabled || _imagingCanvas.Width <= 0 || _imagingCanvas.Height <= 0) return;

            _imagingCanvas.Children.Clear();

            double width = _imagingCanvas.Width;
            double height = _imagingCanvas.Height;

            var visiblePoints = ImagingData.Points
                .Where(p => p.Depth >= MinDepth && p.Depth <= MaxDepth)
                .OrderBy(p => p.Depth)
                .ToList();

            if (!visiblePoints.Any()) return;

            double rectHeight = height / visiblePoints.Count;

            for (int i = 0; i < visiblePoints.Count; i++)
            {
                var point = visiblePoints[i];
                double y = i * rectHeight;

                var rect = new Rectangle
                {
                    Width = width,
                    Height = rectHeight,
                    Fill = CreateGradientBrush(point)
                };

                Canvas.SetLeft(rect, 0);
                Canvas.SetTop(rect, y);
                _imagingCanvas.Children.Add(rect);
            }
        }

        private LinearGradientBrush CreateGradientBrush(GammaImagingPoint point)
        {
            var brush = new LinearGradientBrush();

            if (SectorCount == 2)
            {
                // 2扇区：上下渐变
                brush.StartPoint = new RelativePoint(0, 0, RelativeUnit.Relative);
                brush.EndPoint = new RelativePoint(0, 1, RelativeUnit.Relative);

                var upColor = ValueToColor(point.UpValue);
                var downColor = ValueToColor(point.DownValue);

                brush.GradientStops.Add(new GradientStop { Color = upColor, Offset = 0 });
                brush.GradientStops.Add(new GradientStop { Color = downColor, Offset = 1 });
            }
            else if (SectorCount == 4)
            {
                // 4扇区：径向渐变效果
                brush.StartPoint = new RelativePoint(0, 0.5, RelativeUnit.Relative);
                brush.EndPoint = new RelativePoint(1, 0.5, RelativeUnit.Relative);

                var leftColor = ValueToColor(point.LeftValue);
                var upColor = ValueToColor(point.UpValue);
                var downColor = ValueToColor(point.DownValue);
                var rightColor = ValueToColor(point.RightValue);

                brush.GradientStops.Add(new GradientStop { Color = leftColor, Offset = 0 });
                brush.GradientStops.Add(new GradientStop { Color = upColor, Offset = 0.33 });
                brush.GradientStops.Add(new GradientStop { Color = downColor, Offset = 0.67 });
                brush.GradientStops.Add(new GradientStop { Color = rightColor, Offset = 1 });
            }

            return brush;
        }

        private Color ValueToColor(double value)
        {
            // 将Gamma值映射到颜色
            double normalizedValue = Math.Max(0, Math.Min(1, (value - MinGammaValue) / (MaxGammaValue - MinGammaValue)));

            // 使用热力图颜色映射：蓝色 -> 绿色 -> 黄色 -> 红色
            if (normalizedValue < 0.25)
            {
                // 蓝色到青色
                double t = normalizedValue * 4;
                return Color.FromRgb(0, (byte)(t * 255), 255);
            }
            else if (normalizedValue < 0.5)
            {
                // 青色到绿色
                double t = (normalizedValue - 0.25) * 4;
                return Color.FromRgb(0, 255, (byte)((1 - t) * 255));
            }
            else if (normalizedValue < 0.75)
            {
                // 绿色到黄色
                double t = (normalizedValue - 0.5) * 4;
                return Color.FromRgb((byte)(t * 255), 255, 0);
            }
            else
            {
                // 黄色到红色
                double t = (normalizedValue - 0.75) * 4;
                return Color.FromRgb(255, (byte)((1 - t) * 255), 0);
            }
        }

        #endregion

        #region 辅助方法

        private void ShowDataAtPosition(Point position)
        {
            // TODO: 实现鼠标悬停显示数据点信息
        }

        private void ZoomIn()
        {
            DepthRange = Math.Max(20, DepthRange * 0.8);
            UpdateDepthRange();
            Refresh();
        }

        private void ZoomOut()
        {
            DepthRange = Math.Min(500, DepthRange * 1.25);
            UpdateDepthRange();
            Refresh();
        }

        #endregion
    }

    #region 数据模型

    /// <summary>
    /// Gamma数据系列
    /// </summary>
    public class GammaSeries
    {
        public string Name { get; set; }
        public IBrush Color { get; set; }
        public double StrokeThickness { get; set; } = 2;
        public List<GammaDataPoint> Points { get; set; } = new List<GammaDataPoint>();
        public bool IsVisible { get; set; } = true;
    }

    /// <summary>
    /// Gamma数据点
    /// </summary>
    public class GammaDataPoint
    {
        public double Depth { get; set; }
        public double Value { get; set; }
    }

    /// <summary>
    /// Gamma成像数据
    /// </summary>
    public class GammaImagingData
    {
        public List<GammaImagingPoint> Points { get; set; } = new List<GammaImagingPoint>();

        public void Clear()
        {
            Points.Clear();
        }
    }

    /// <summary>
    /// Gamma成像数据点
    /// </summary>
    public class GammaImagingPoint
    {
        public double Depth { get; set; }
        public double UpValue { get; set; }
        public double DownValue { get; set; }
        public double LeftValue { get; set; }
        public double RightValue { get; set; }
    }

    #endregion
}
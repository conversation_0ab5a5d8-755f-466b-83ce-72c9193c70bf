using System;
using System.Collections.Generic;
using System.Linq;
using TartanLogApp.DataSource;

namespace TartanLogApp.CurveControl
{
    /// <summary>
    /// Gamma数据适配器 - 连接MeztlDB数据和曲线控件
    /// </summary>
    public class GammaDataAdapter
    {
        private readonly GammaCurveControl _curveControl;
        private readonly MeztlDB _database;

        public GammaDataAdapter(GammaCurveControl curveControl, MeztlDB database)
        {
            _curveControl = curveControl;
            _database = database;
        }

        /// <summary>
        /// 初始化曲线系列
        /// </summary>
        public void InitializeSeries(List<string> grChannels)
        {
            System.Console.WriteLine($"[Gamma适配器] 开始初始化系列 - 通道数: {grChannels.Count}");

            _curveControl.Series.Clear();

            foreach (var channel in grChannels)
            {
                _curveControl.AddSeries(channel);
                System.Console.WriteLine($"[Gamma适配器] 添加系列: {channel}");
            }

            System.Console.WriteLine($"[Gamma适配器] 系列初始化完成 - 总计: {_curveControl.Series.Count}个系列");
        }

        /// <summary>
        /// 更新曲线数据
        /// </summary>
        public void UpdateCurveData(MezDataSet dataSet)
        {
            if (dataSet?.GrList == null)
            {
                System.Console.WriteLine("[Gamma适配器] 数据集为空或GrList为空");
                return;
            }

            System.Console.WriteLine($"[Gamma适配器] 开始更新曲线数据 - GR通道数: {dataSet.GrList.Length}");

            int totalPointsAdded = 0;

            foreach (var grData in dataSet.GrList)
            {
                if (grData?.grList == null)
                {
                    System.Console.WriteLine($"[Gamma适配器] 跳过空数据通道");
                    continue;
                }

                System.Console.WriteLine($"[Gamma适配器] 处理通道: {grData.GrName}, 数据点数: {grData.grList.Count}");

                // 清空现有数据
                var series = _curveControl.Series.FirstOrDefault(s => s.Name == grData.GrName);
                if (series != null)
                {
                    series.Points.Clear();

                    // 添加新数据点
                    foreach (var point in grData.grList)
                    {
                        series.Points.Add(new GammaDataPoint
                        {
                            Depth = point.depth,
                            Value = point.val
                        });
                        totalPointsAdded++;
                    }

                    System.Console.WriteLine($"[Gamma适配器] 通道 {grData.GrName} 更新完成 - 添加了 {grData.grList.Count} 个数据点");
                }
                else
                {
                    System.Console.WriteLine($"[Gamma适配器] 警告: 未找到对应的系列 {grData.GrName}");
                }
            }

            System.Console.WriteLine($"[Gamma适配器] 数据更新完成 - 总计添加 {totalPointsAdded} 个数据点");
            System.Console.WriteLine("[Gamma适配器] 开始刷新曲线显示");

            _curveControl.Refresh();

            System.Console.WriteLine("[Gamma适配器] 曲线显示刷新完成");
        }

        /// <summary>
        /// 设置Gamma成像数据
        /// </summary>
        public void SetupGammaImaging(List<string> selectedChannels, int sectorCount)
        {
            if (selectedChannels.Count < sectorCount) return;

            var imagingPoints = CreateImagingData(selectedChannels, sectorCount);
            _curveControl.SetImagingData(imagingPoints, sectorCount);
        }

        private List<GammaImagingPoint> CreateImagingData(List<string> channels, int sectorCount)
        {
            var imagingPoints = new List<GammaImagingPoint>();

            // 获取所有通道的数据点
            var channelData = new Dictionary<string, List<GammaDataPoint>>();

            foreach (var channel in channels)
            {
                var series = _curveControl.Series.FirstOrDefault(s => s.Name == channel);
                if (series != null)
                {
                    channelData[channel] = series.Points.ToList();
                }
            }

            if (!channelData.Any()) return imagingPoints;

            // 找到所有深度点的交集
            var allDepths = channelData.Values
                .SelectMany(points => points.Select(p => p.Depth))
                .Distinct()
                .OrderBy(d => d)
                .ToList();

            foreach (var depth in allDepths)
            {
                var imagingPoint = new GammaImagingPoint { Depth = depth };

                if (sectorCount == 2)
                {
                    // 2扇区：上、下
                    imagingPoint.UpValue = GetValueAtDepth(channelData, channels[0], depth);
                    imagingPoint.DownValue = GetValueAtDepth(channelData, channels[1], depth);
                }
                else if (sectorCount == 4 && channels.Count >= 4)
                {
                    // 4扇区：上、下、左、右
                    imagingPoint.UpValue = GetValueAtDepth(channelData, channels[0], depth);
                    imagingPoint.DownValue = GetValueAtDepth(channelData, channels[1], depth);
                    imagingPoint.LeftValue = GetValueAtDepth(channelData, channels[2], depth);
                    imagingPoint.RightValue = GetValueAtDepth(channelData, channels[3], depth);
                }

                imagingPoints.Add(imagingPoint);
            }

            return imagingPoints;
        }

        private double GetValueAtDepth(Dictionary<string, List<GammaDataPoint>> channelData, string channel, double depth)
        {
            if (!channelData.ContainsKey(channel)) return 0;

            var points = channelData[channel];
            var exactPoint = points.FirstOrDefault(p => Math.Abs(p.Depth - depth) < 0.01);

            if (exactPoint != null)
            {
                return exactPoint.Value;
            }

            // 线性插值
            var beforePoint = points.Where(p => p.Depth <= depth).OrderByDescending(p => p.Depth).FirstOrDefault();
            var afterPoint = points.Where(p => p.Depth >= depth).OrderBy(p => p.Depth).FirstOrDefault();

            if (beforePoint != null && afterPoint != null && beforePoint.Depth != afterPoint.Depth)
            {
                double ratio = (depth - beforePoint.Depth) / (afterPoint.Depth - beforePoint.Depth);
                return beforePoint.Value + ratio * (afterPoint.Value - beforePoint.Value);
            }

            return beforePoint?.Value ?? afterPoint?.Value ?? 0;
        }
    }
}
﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Runtime;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using TartanLogApp.Models;
using TartanLogApp.Properties;

namespace TartanLogApp.CurveControl
{
    /// <summary>
    /// LogCurveControl.xaml 的交互逻辑
    /// </summary>
    public partial class LogCurveImgControl : UserControl
    {
        public LogCurveImgControl()
        {
            InitializeComponent();
            //HeaderArea.Children.Clear();
            //CurveArea.Children.Clear();
            _Series = new List<Series>();
            GammaImgData = null;
        }
        private List<Series> _Series;
        public List<Series> Series
        {
            get => _Series;
            set
            {
                _Series = value;
                //UpdateChart();
            }
        }

        public double MinDepth { get; set; }
        public double MaxDepth { get; set; }

        public double XScaleMin { get; set; }
        public double XScaleMax { get; set; }

        public double YScaleMin { get; set; }
        public double YScaleMax { get; set; }

        public double XGridInterval { get; set; }
        public double YGridInterval => YMajorGridInterval / 5;

        public double XMajorGridInterval { get; set; }
        public double YMajorGridInterval { get; set; }

        private Brush GridStroke= new SolidColorBrush(Color.FromRgb(51, 153, 102));
        private double GridThickness = 1;
        private const int MaxPoints = 3000;

        private int cntYStep;
        private double YfirstVal;

        private bool ChartUpdate = false;
        private bool GammaImg = false;
        private int sector;
        private GammaImgItem[] GammaImgData;
        private Dictionary<string, string> tagNameToAzm = new Dictionary<string, string>();
        
        /// <summary>
        /// 设置Gamma成像数据
        /// </summary>
        /// <param name="args">Gamma成像顺序上，下，左，右</param>
        /// <returns></returns>
        public bool SetGammaImg(string[] args)
        {
            tagNameToAzm.Clear();
            int l = args.Length;
            if (l == 2 || l == 4)
            {
                sector = l;
                                
                double ImgMin,ImgMax;

                ImgMin = MinDepth;
                ImgMax = MaxDepth;

                var list = Series.Where(o => o.Title == args[0]).First();
                var list1 = Series.Where(o => o.Title == args[1]).First();
               
                double Max = list.Values.Max(o => o.Item1);
                double Min = list.Values.Min(o => o.Item1);
                double Min1 = list1.Values.Min(o => o.Item1);
                double Max1 = list1.Values.Max(o => o.Item1);

                if (Min > ImgMin) ImgMin = Min;
                if (Min1 > ImgMin) ImgMin = Min1;

                if (Max < ImgMax) ImgMax = Max;
                if (Max1 < ImgMax) ImgMax = Max1;

                Series list2 = null;
                Series list3 = null;
                tagNameToAzm.Add(args[0], "上");
                tagNameToAzm.Add(args[1], "下");
               
                if (!ChartSettings.IsGammaSetting)
                {
                    ChartSettings.minGammaImg = (int)(list.Values.Min(o => o.Item2) * 10) / 10.0;
                    ChartSettings.maxGammaImg = (int)(list.Values.Max(o => o.Item2) * 10) / 10.0;
                }

                if (sector > 3)
                {
                    list2 = Series.Where(o => o.Title == args[2]).First();
                    list3 = Series.Where(o => o.Title == args[3]).First();
                    tagNameToAzm.Add(args[2], "左");
                    tagNameToAzm.Add(args[3], "右");

                    double Max2 = list2.Values.Max(o => o.Item1);
                    double Min2 = list2.Values.Min(o => o.Item1);
                    double Max3 = list3.Values.Max(o => o.Item1);
                    double Min3 = list3.Values.Min(o => o.Item1);

                    if (Math.Max(Min2, Min3) > ImgMin) ImgMin = Math.Max(Min2, Min3);
                    if (Math.Min(Max2, Max3) < ImgMax) ImgMax = Math.Min(Max2, Max3);

                }

                var ups = list.Values.Where(o => o.Item1 >= ImgMin && o.Item1 <= ImgMax).ToArray();
                var downs = list1.Values.Where(o => o.Item1 >= ImgMin && o.Item1 <= ImgMax).ToArray();
                int cnt = ups.Count();
                GammaImgData = new GammaImgItem[cnt];
       
                Tuple<double, double>[] left = null;
                Tuple<double, double>[] right = null;

                if (sector > 3)
                {
                    left = list2.Values.Where(o => o.Item1 >= ImgMin && o.Item1 <= ImgMax).ToArray();
                    right = list3.Values.Where(o => o.Item1 >= ImgMin && o.Item1 <= ImgMax).ToArray();
                }

                for (int i = 0; i < cnt; i++)
                {
                    GammaImgData[i] = new GammaImgItem();
                    GammaImgData[i].depth = ups[i].Item1;
                    GammaImgData[i].UpGamma = ups[i].Item2;
                    GammaImgData[i].DownGamma = downs[i].Item2;
                   
                    if (sector > 3)
                    {
                        GammaImgData[i].LeftGamma = left[i].Item2;
                        GammaImgData[i].RightGamma = right[i].Item2;
                    }
                }

                GammaImg = true;

                UserSettings.Instance.sector = sector;
                UserSettings.Instance.GammaImging = true;
                UserSettings.Instance.tagNameToAzm = tagNameToAzm;

                Refresh();
                return true;
            }
            else
            {
                sector = 0;
                return false;
            }

        }

        public void ClearCanvas()
        {            
            ChartUpdate = false;
            GammaImg = false;
            GammaImgData = null;
            HeaderArea.Children.Clear();
            CurveArea.Children.Clear();
            RulerArea.Children.Clear();
            ImgHeader.Children.Clear();
            ImgArea.Children.Clear();
        }

        public void UpdateChart()
        {
            DataInit();
            ChartUpdate = true;
            HeaderArea.Children.Clear();
            CurveArea.Children.Clear();
            RulerArea.Children.Clear();
            DrawHeader();
            DrawGrid();
            DrawCurve();

        }

        public void Refresh()
        {
            GC.Collect();
            if (!ChartUpdate) return;
            HeaderArea.Children.Clear();
            CurveArea.Children.Clear();
            RulerArea.Children.Clear();
            ImgArea.Children.Clear();
            ImgHeader.Children.Clear();
            DrawHeader();
            DrawGrid();
            DrawCurve();
            DrawImgHeader();
            DrawGammaImg();
        }

        public BitmapSource ExportImg(string wellname) 
        {
            if (!ChartUpdate) return null;

            DrawingVisual drawingVisual = new DrawingVisual();
            int width, height, headerheight, rulerwidth, curvewidth, curveheight;
            int margin, titleheight;
            margin = 10;
            titleheight = 60;
            rulerwidth = 50;
            curvewidth = 250;
            headerheight = 140;
            width = 2 * margin + rulerwidth + curvewidth * 2;

            // 104*630=65520
            if ((MaxDepth - MinDepth) / cntYStep / YMajorGridInterval > 103)
            {
                cntYStep = 7;
                YMajorGridInterval =(int) (MaxDepth - MinDepth) / 721;
                if (YMajorGridInterval < 5)
                    YMajorGridInterval = 5;
                else if(YMajorGridInterval < 10)
                {
                    YMajorGridInterval = 10;
                }
                else
                {
                    YMajorGridInterval = (int)(YMajorGridInterval / 10) * 10;
                }
            }

            curveheight = (int)(headerheight / 2 * 9 / YMajorGridInterval * (MaxDepth - MinDepth) / cntYStep);

            // height < 65535
            height = curveheight + 2 * margin + titleheight + headerheight;
            
            using (DrawingContext dc = drawingVisual.RenderOpen())
            {

                dc.DrawRectangle(Brushes.White, null, new Rect(0, 0, width, height));

                Pen outlinePen = new Pen(Brushes.Black, 2);
           
                //画标题
                FormattedText lbGraphTitle = new FormattedText(wellname.Trim(), CultureInfo.GetCultureInfo("zh-cn"), FlowDirection.LeftToRight, new Typeface("Arial"), 24, Brushes.Black, 1);
                lbGraphTitle.SetFontWeight(FontWeights.Bold);
                double len = lbGraphTitle.Width;
                dc.DrawText(lbGraphTitle, new Point((width - len) / 2, margin + 10));
                Rect rect1 = new Rect
                {
                    X = width - margin - 122,
                    Y = 38,
                    Width = 119,
                    Height = 30,
                };
                dc.DrawImage(MainWindow.logo, rect1);
               
                //大边框分块
                dc.DrawRectangle(null, outlinePen, new Rect(margin + rulerwidth, margin + titleheight, 2 * curvewidth, curveheight + headerheight));
                dc.DrawLine(outlinePen, new Point(margin + rulerwidth, margin + titleheight + headerheight), new Point(width - margin, margin + titleheight + headerheight));
                dc.DrawLine(outlinePen, new Point(margin + rulerwidth + curvewidth, margin + titleheight), new Point(margin + rulerwidth + curvewidth, height - margin));

                if (Series.Count < 1)
                {
                    goto over;
                }

                #region Curve Header

                int seriesCnt = Series.Count;               
                int k = 0;
                double gridInterval = (double)headerheight / (seriesCnt + 1);
               
                foreach (var serie in Series)
                {
                    k++;
                    Pen serieLegendPen = new Pen(serie.Stroke, serie.StrokeThickness);                    
                    dc.DrawLine(serieLegendPen, new Point(margin + rulerwidth, margin + titleheight + k * gridInterval), new Point(margin + rulerwidth + curvewidth, margin + titleheight + k * gridInterval));

                    FormattedText lbminVal = new FormattedText(serie.MinVal.ToString(), CultureInfo.GetCultureInfo("zh-cn"), FlowDirection.LeftToRight, new Typeface("Arial"), 12, serie.Stroke, 1);
                    dc.DrawText(lbminVal, new Point(margin + rulerwidth + 3, margin + titleheight + k * gridInterval - 14));

                    FormattedText lbmaxVal = new FormattedText(serie.MaxVal.ToString(), CultureInfo.GetCultureInfo("zh-cn"), FlowDirection.LeftToRight, new Typeface("Arial"), 12, serie.Stroke, 1);
                    double wid = lbmaxVal.Width;
                    dc.DrawText(lbmaxVal, new Point(margin + rulerwidth + curvewidth - wid - 3, margin + titleheight + k * gridInterval - 14));
                    
                    string gvName = serie.Title;
                    tagNameToAzm.TryGetValue(gvName, out string azm);

                    if (GammaImg && azm != null)
                        gvName += $"({azm})";

                    FormattedText lbtitle = new FormattedText(gvName, CultureInfo.GetCultureInfo("zh-cn"), FlowDirection.LeftToRight, new Typeface("Arial"), 12, serie.Stroke, 1);
                    lbtitle.SetFontWeight(FontWeights.Bold);
                    double ww = lbtitle.Width;
                    dc.DrawText(lbtitle, new Point(margin + rulerwidth + (curvewidth - ww) / 2, margin + titleheight + k * gridInterval - 14));

                }


                #endregion

                #region Grid and Depth Ruler

                FormattedText formatted = new FormattedText("井深\n\n m", CultureInfo.GetCultureInfo("zh-cn"),FlowDirection.LeftToRight, new Typeface("Arial"),16,Brushes.Black,1);
                formatted.SetFontWeight(FontWeights.Bold);

                dc.DrawText(formatted, new Point(margin + 5, margin + titleheight + 50));
                                
                double yscale = curveheight / (MaxDepth - MinDepth);

                Pen BigYGridPen = new Pen(GridStroke, 2);
                Pen dashGridPen = new Pen();
                BigYGridPen.Freeze();
                
                dashGridPen.Brush = GridStroke;
                dashGridPen.Thickness = 1;
                dashGridPen.DashStyle = new DashStyle(new DoubleCollection { 5, 2 },0);                
                dashGridPen.Freeze();

                double depth = MinDepth + YGridInterval;
                double depthPixCur = margin + titleheight + headerheight + YGridInterval * yscale;
                int BigGrid = 0;
                do
                {
                    BigGrid++;
                    if (BigGrid == 5)
                    {
                        dc.DrawLine(BigYGridPen, new Point(margin + rulerwidth + 1, depthPixCur), new Point(margin + rulerwidth + curvewidth - 1, depthPixCur));
                    
                        FormattedText lable = new FormattedText(depth.ToString(), CultureInfo.GetCultureInfo("zh-cn"), FlowDirection.LeftToRight, new Typeface("Arial"), 12, Brushes.Black, 1);
                        lable.SetFontWeight(FontWeights.Bold);

                        dc.DrawText(lable, new Point(margin + 5, depthPixCur - 5));

                        BigGrid = 0;
                    }
                    else
                    {
                        dc.DrawLine(dashGridPen, new Point(margin + rulerwidth + 1, depthPixCur), new Point(margin + rulerwidth + curvewidth - 1, depthPixCur));
                    }

                    depth += YGridInterval;
                    depthPixCur += YGridInterval * yscale;

                } while (depth < MaxDepth);

                for (int i = 1; i < 10; i++)
                {
                    dc.DrawLine(dashGridPen, new Point(margin + rulerwidth + i * curvewidth / 10, height - curveheight - margin), new Point(margin + rulerwidth + i * curvewidth / 10, height - margin));
                }

                #endregion
                              
                #region Curve Area
                
                double h = curveheight;
                double w = curvewidth;
                double xOffset = margin + rulerwidth;
                double yOffset = height - margin - curveheight;
               
                double yStep = h / (MaxDepth - MinDepth);

                foreach (var serie in Series)
                {
                    XScaleMax = serie.MaxVal;
                    XScaleMin = serie.MinVal;

                    double xStep = w / (XScaleMax - XScaleMin);

                    PathGeometry geometry = new PathGeometry();
                    PathFigure f = new PathFigure();
                    var x = serie.Values.FirstOrDefault(o => o.Item1 >= MinDepth);
                    if (x == null) continue;
                    f.StartPoint = new Point((x.Item2 - XScaleMin) * xStep + xOffset, (x.Item1 - MinDepth) * yStep + yOffset);

                    var shows = serie.Values.Where(o => o.Item1 >= MinDepth && o.Item1 <= MaxDepth + 0.1);

                    foreach (var item in shows)
                    {
                        Point p = new Point
                        {
                            X = (item.Item2 - XScaleMin) * xStep + xOffset,
                            Y = (item.Item1 - MinDepth) * yStep + yOffset
                        };
                        LineSegment segment = new LineSegment
                        {
                            Point = p
                        };
                        segment.Freeze();
                        f.Segments.Add(segment);
                    }
                   
                    geometry.Figures.Add(f);
                    
                    geometry.Freeze();
                    Pen CurvePen = new Pen(serie.Stroke, serie.StrokeThickness);

                    dc.DrawGeometry(null, CurvePen, geometry);
                }

                #endregion

                if (!GammaImg || sector == 0)
                {
                    goto over;
                }

                #region Gamma Imaging Header

                LinearGradientBrush gradientBrush = new LinearGradientBrush();

                double min = ChartSettings.minGammaImg;
                double max = ChartSettings.maxGammaImg;
                double scale = max - min;
                int type = ChartSettings.ImgColorType;

                Color color1, color2, color3, color4, color5;

                if (type == 0)
                {
                    color1 = Colors.White;
                    color2 = Colors.Gold;
                    color3 = Colors.Chocolate;
                    color4 = Colors.DarkRed;
                    color5 = Colors.Black;
                    gradientBrush.GradientStops.Add(new GradientStop { Color = color1, Offset = 0 });
                    gradientBrush.GradientStops.Add(new GradientStop { Color = color2, Offset = 0.25 });
                    gradientBrush.GradientStops.Add(new GradientStop { Color = color3, Offset = 0.5 });
                    gradientBrush.GradientStops.Add(new GradientStop { Color = color4, Offset = 0.75 });
                    gradientBrush.GradientStops.Add(new GradientStop { Color = color5, Offset = 1 });

                }
                else
                {
                    color1 = Colors.Blue;
                    color2 = Colors.Cyan;
                    color3 = Colors.Lime;
                    color4 = Colors.Yellow;
                    color5 = Colors.Red;
                    gradientBrush.GradientStops.Add(new GradientStop { Color = color1, Offset = 0 });
                    gradientBrush.GradientStops.Add(new GradientStop { Color = color2, Offset = 0.25 });
                    gradientBrush.GradientStops.Add(new GradientStop { Color = color3, Offset = 0.5 });
                    gradientBrush.GradientStops.Add(new GradientStop { Color = color4, Offset = 0.75 });
                    gradientBrush.GradientStops.Add(new GradientStop { Color = color5, Offset = 1 });

                }

                gradientBrush.StartPoint = new Point(0, 0.5);
                gradientBrush.EndPoint = new Point(1, 0.5);
                Rect rect = new Rect();
                rect.Width = curvewidth * 0.8;
                rect.Height = headerheight * 0.16;
                rect.X = 1.1 * curvewidth + margin + rulerwidth;
                rect.Y = 0.25 * headerheight + margin + titleheight;
                dc.DrawRectangle(gradientBrush, null, rect);

                FormattedText lbminGa = new FormattedText(min.ToString(), CultureInfo.GetCultureInfo("zh-cn"), FlowDirection.LeftToRight, new Typeface("Arial"), 12, Brushes.Black, 1);
                FormattedText lbmaxGa = new FormattedText(max.ToString(), CultureInfo.GetCultureInfo("zh-cn"), FlowDirection.LeftToRight, new Typeface("Arial"), 12, Brushes.Black, 1);
                dc.DrawText(lbminGa, new Point(width - margin - 0.9 * curvewidth, 0.45 * headerheight + margin + titleheight));
                dc.DrawText(lbmaxGa, new Point(width - margin - 0.16 * curvewidth, 0.45 * headerheight + margin + titleheight));

                FormattedText txtUp = new FormattedText("上", CultureInfo.GetCultureInfo("zh-cn"), FlowDirection.LeftToRight, new Typeface("Arial"), 12, Brushes.Black, 1);
                FormattedText txtDown = new FormattedText("下", CultureInfo.GetCultureInfo("zh-cn"), FlowDirection.LeftToRight, new Typeface("Arial"), 12, Brushes.Black, 1);
                FormattedText txtleft = new FormattedText("左", CultureInfo.GetCultureInfo("zh-cn"), FlowDirection.LeftToRight, new Typeface("Arial"), 12, Brushes.Black, 1);
                FormattedText txtright = new FormattedText("右", CultureInfo.GetCultureInfo("zh-cn"), FlowDirection.LeftToRight, new Typeface("Arial"), 12, Brushes.Black, 1);

                dc.DrawText(txtUp, new Point(width - margin - curvewidth + 3, headerheight + margin + titleheight - 14));
                dc.DrawText(txtUp, new Point(width - margin - 14, headerheight + margin + titleheight - 14));
                dc.DrawText(txtDown, new Point(width - margin - 0.53 * curvewidth, headerheight + margin + titleheight - 14));

                if (sector > 3)
                {
                    dc.DrawText(txtleft, new Point(width - margin - 0.27 * curvewidth, headerheight + margin + titleheight - 14));
                    dc.DrawText(txtright, new Point(width - margin - 0.77 * curvewidth, headerheight + margin + titleheight - 14));
                }

                #endregion

                #region GammaImagingArea

                xOffset = margin + rulerwidth + curvewidth + 1;

                var gammashows = GammaImgData.Where(o => o.depth >= MinDepth && o.depth <= MaxDepth);

                foreach (var item in gammashows)
                {
                    Rect rectangle = new Rect
                    {
                        Height = 0.1 * yStep * 1.0,
                        Width = w - 2,
                        X = xOffset,
                        Y = (item.depth - MinDepth) * yStep + yOffset
                    };

                    LinearGradientBrush gradientRectBrush = new LinearGradientBrush();

                    if (sector == 2)
                    {
                        color1 = UniformedColor((item.UpGamma - min) / scale, type);
                        color2 = UniformedColor((item.DownGamma - min) / scale, type);
                        gradientRectBrush.GradientStops.Add(new GradientStop { Color = color1, Offset = 0 });
                        gradientRectBrush.GradientStops.Add(new GradientStop { Color = color2, Offset = 0.5 });
                        gradientRectBrush.GradientStops.Add(new GradientStop { Color = color1, Offset = 1 });
                    }
                    else
                    {
                        color1 = UniformedColor((item.UpGamma - min) / scale, type);
                        color2 = UniformedColor((item.DownGamma - min) / scale, type);
                        color3 = UniformedColor((item.LeftGamma - min) / scale, type);
                        color4 = UniformedColor((item.RightGamma - min) / scale, type);

                        gradientRectBrush.GradientStops.Add(new GradientStop { Color = color1, Offset = 0 });
                        gradientRectBrush.GradientStops.Add(new GradientStop { Color = color4, Offset = 0.25 });
                        gradientRectBrush.GradientStops.Add(new GradientStop { Color = color2, Offset = 0.5 });
                        gradientRectBrush.GradientStops.Add(new GradientStop { Color = color3, Offset = 0.75 });
                        gradientRectBrush.GradientStops.Add(new GradientStop { Color = color1, Offset = 1 });

                    }
                    gradientRectBrush.StartPoint = new Point(0, 0.5);
                    gradientRectBrush.EndPoint = new Point(1, 0.5);

                    dc.DrawRectangle(gradientRectBrush, null, rectangle);
                }

                #endregion
                
            over:
                    
                dc.Close();

            }

            RenderTargetBitmap renderTarget = new RenderTargetBitmap(width, height, 96, 96, PixelFormats.Pbgra32);
            renderTarget.Render(drawingVisual);
            
            return renderTarget;
        }

        private void DataInit()
        {           
            YMajorGridInterval = 20;
            MaxDepth += YMajorGridInterval;

            cntYStep = 6;
            YScaleMax = MaxDepth;
            YScaleMin = YScaleMax - cntYStep * YMajorGridInterval;

            if (YMajorGridInterval > 10)
                YfirstVal = (int)(YScaleMin / 10) * 10 + 10;
            else
                YfirstVal = ((int)(YScaleMin / YMajorGridInterval) + 1) * YMajorGridInterval;

        }

        private void DrawHeader()
        {
            HeaderArea.Children.Clear();
            int seriesCnt = Series.Count;
            double h = HeaderArea.ActualHeight;
            double w = HeaderArea.ActualWidth;
            int k = 0;
            double gridInterval = h / (seriesCnt + 1);
            foreach (var serie in Series)
            {
                k++;
                Line serieLegend = new Line();
                serieLegend.Stroke = serie.Stroke;
                serieLegend.StrokeThickness = serie.StrokeThickness;
                serieLegend.X1 = 0;
                serieLegend.X2 = w;
                serieLegend.Y1 = k * gridInterval;
                serieLegend.Y2 = k * gridInterval;

                TextBlock tbMin = new TextBlock();
                tbMin.Text = serie.MinVal.ToString();
                tbMin.Foreground = serie.Stroke;
                HeaderArea.Children.Add(tbMin);
                Canvas.SetLeft(tbMin, 0);
                Canvas.SetBottom(tbMin, (seriesCnt + 1 - k) * gridInterval);

                string gvName = serie.Title;
                tagNameToAzm.TryGetValue(gvName, out string azm);
               
                if (GammaImg && azm != null)
                    gvName += $"({azm})";

                TextBlock tbTitle = new TextBlock();
                tbTitle.Text = gvName;
                tbTitle.Foreground = serie.Stroke;
                tbTitle.FontWeight = FontWeights.Bold;
                HeaderArea.Children.Add(tbTitle);
                Canvas.SetLeft(tbTitle, w / 2 - 10);
                Canvas.SetBottom(tbTitle, (seriesCnt + 1 - k) * gridInterval);

                TextBlock tbMax = new TextBlock();
                tbMax.Text = serie.MaxVal.ToString();
                tbMax.Foreground = serie.Stroke;
                HeaderArea.Children.Add(tbMax);
                Canvas.SetRight(tbMax, 0);
                Canvas.SetBottom(tbMax, (seriesCnt + 1 - k) * gridInterval);

                HeaderArea.Children.Add(serieLegend);
            }

        }

        private void DrawGrid()
        {
            double h = CurveArea.ActualHeight;
            double w = CurveArea.ActualWidth;
            int XgridCnt = 10;            
            double yStep = h / cntYStep;
            double xStep = w / XgridCnt;

            double YfirstPos = (YfirstVal - YScaleMin) / YMajorGridInterval * yStep;

            //绘制井深轴大间隔线
            for (int i = 0; i < cntYStep; i++)
            {
                Line xlineGrid = new Line();
                xlineGrid.Stroke = GridStroke;

                xlineGrid.StrokeThickness = 2;
                xlineGrid.X1 = 0;
                xlineGrid.X2 = w;
                xlineGrid.Y1 = i * yStep + YfirstPos;
                xlineGrid.Y2 = i * yStep + YfirstPos;

                CurveArea.Children.Add(xlineGrid);

                TextBlock textBlock = new TextBlock();
                textBlock.Text = (YMajorGridInterval * i + YfirstVal).ToString();

                Canvas.SetLeft(textBlock, 8);
                Canvas.SetTop(textBlock, i * yStep + YfirstPos - 5);
                RulerArea.Children.Add(textBlock);

            }

            //绘制井深轴小间隔线
            double yVal = YfirstVal;
            for (int i = 0; ; i++)
            {
                Line xlineGrid = new Line();
                xlineGrid.Stroke = GridStroke;
                xlineGrid.StrokeDashArray = new DoubleCollection { 5, 2 };
                xlineGrid.StrokeThickness = 1;
                xlineGrid.X1 = 0;
                xlineGrid.X2 = w;
                xlineGrid.Y1 = YfirstPos - i * yStep / YMajorGridInterval * YGridInterval;
                xlineGrid.Y2 = YfirstPos - i * yStep / YMajorGridInterval * YGridInterval;

                CurveArea.Children.Add(xlineGrid);

                yVal -= YGridInterval;
                if (yVal < YScaleMin) break;
            }
            yVal = YfirstVal;
            for (int i = 0; ; i++)
            {
                Line xlineGrid = new Line();
                xlineGrid.Stroke = GridStroke;
                xlineGrid.StrokeDashArray = new DoubleCollection { 5, 2 };
                xlineGrid.StrokeThickness = 1;
                xlineGrid.X1 = 0;
                xlineGrid.X2 = w;
                xlineGrid.Y1 = i * yStep / YMajorGridInterval * YGridInterval + YfirstPos;
                xlineGrid.Y2 = i * yStep / YMajorGridInterval * YGridInterval + YfirstPos;

                CurveArea.Children.Add(xlineGrid);

                yVal += YGridInterval;
                if (yVal > YScaleMax) break;

            }

            //绘制数值轴小间隔线
            for (int i = 1; i < XgridCnt; i++)
            {
                Line ylineGrid = new Line();
                ylineGrid.Stroke = GridStroke;
                ylineGrid.StrokeDashArray = new DoubleCollection { 5, 2 };
                ylineGrid.StrokeThickness = 1;
                ylineGrid.X1 = i * xStep;
                ylineGrid.X2 = i * xStep;
                ylineGrid.Y1 = 0;
                ylineGrid.Y2 = h;

                CurveArea.Children.Add(ylineGrid);
            }

        }

        private void DrawCurve()
        {
            foreach (var serie in Series)
            {
                XScaleMax = serie.MaxVal;
                XScaleMin = serie.MinVal;

                double h = CurveArea.ActualHeight;
                double w = CurveArea.ActualWidth;

                double xStep = w / (XScaleMax - XScaleMin);
                double yStep = h / cntYStep / YMajorGridInterval;

                Path path = new Path();
                path.Stroke = serie.Stroke;
                path.StrokeThickness = serie.StrokeThickness;
                path.Fill = null;

                PathGeometry geometry = new PathGeometry();
                PathFigure f = new PathFigure();
                var x = serie.Values.FirstOrDefault(o => o.Item1 >= YScaleMin);
                if (x == null) continue;
                f.StartPoint = new Point((x.Item2 - XScaleMin) * xStep, (x.Item1 - YScaleMin) * yStep);

                var shows0 = serie.Values.Where(o => o.Item1 >= YScaleMin - 0.1 && o.Item1 <= YScaleMax + 0.1).ToArray();
                int k = shows0.Count();
                List<Tuple<double,double>> shows = new List<Tuple<double, double>>();
                int itv = 0;
                if (k > MaxPoints)
                {
                    itv = k / MaxPoints;
                }
                for (int i = 0; i < k; i++)
                {
                    shows.Add(shows0[i]);
                    i += itv;
                }

                //System.Diagnostics.Debug.WriteLine(k + "\t" + shows.Count());

                foreach (var item in shows)
                {
                    Point p = new Point
                    {
                        X = (item.Item2 - XScaleMin) * xStep,
                        Y = (item.Item1 - YScaleMin) * yStep
                    };
                    LineSegment segment = new LineSegment
                    {
                        Point = p
                    };
                    segment.IsSmoothJoin = true;
                    f.Segments.Add(segment);
                }

                geometry.Figures.Add(f);
                path.Data = geometry;

                CurveArea.Children.Add(path);

            }
        }

        private void DrawImgHeader()
        {
            if (!GammaImg)
            {
                return;
            }

            ImgHeader.Children.Clear();

            double h = ImgHeader.ActualHeight;
            double w = ImgHeader.ActualWidth;
            
            Rectangle rectangle = new Rectangle();
            //rectangle.Stroke = Brushes.Gray;
            rectangle.Height = 0.16 * h;
            rectangle.Width = w * 0.8;
            Canvas.SetLeft(rectangle, w * 0.1);
            Canvas.SetTop(rectangle, 0.25 * h);

            LinearGradientBrush gradientBrush = new LinearGradientBrush();

            double min = ChartSettings.minGammaImg;
            double max = ChartSettings.maxGammaImg;

            int type = ChartSettings.ImgColorType;

            Color color1, color2, color3, color4, color5; 

            if (type == 0)
            {
                color1 = Colors.White;
                color2 = Colors.Gold; //#FFFFD700
                color3 = Colors.Chocolate; //#FFD2691E
                color4 = Colors.DarkRed;//#FF8B0000
                color5 = Colors.Black;
                gradientBrush.GradientStops.Add(new GradientStop { Color = color1, Offset = 0 });
                gradientBrush.GradientStops.Add(new GradientStop { Color = color2, Offset = 0.25 });
                gradientBrush.GradientStops.Add(new GradientStop { Color = color3, Offset = 0.5 });
                gradientBrush.GradientStops.Add(new GradientStop { Color = color4, Offset = 0.75 });
                gradientBrush.GradientStops.Add(new GradientStop { Color = color5, Offset = 1 });

            }
            else
            {
                color1 = Colors.Blue;
                color2 = Colors.Cyan;
                color3 = Colors.Lime;
                color4 = Colors.Yellow;
                color5 = Colors.Red;
                gradientBrush.GradientStops.Add(new GradientStop { Color = color1, Offset = 0 });
                gradientBrush.GradientStops.Add(new GradientStop { Color = color2, Offset = 0.25 });
                gradientBrush.GradientStops.Add(new GradientStop { Color = color3, Offset = 0.5 });
                gradientBrush.GradientStops.Add(new GradientStop { Color = color4, Offset = 0.75 });
                gradientBrush.GradientStops.Add(new GradientStop { Color = color5, Offset = 1 });

            }

            gradientBrush.StartPoint = new Point(0, 0.5);
            gradientBrush.EndPoint = new Point(1, 0.5);

            rectangle.Fill = gradientBrush;

            ImgHeader.Children.Add(rectangle);

            TextBlock tbMin = new TextBlock();
            tbMin.Text = min.ToString();
            ImgHeader.Children.Add(tbMin);
            Canvas.SetLeft(tbMin, 0.07 * w);
            Canvas.SetTop(tbMin, 0.45 * h);
         
            TextBlock tbMax = new TextBlock();
            tbMax.Text = max.ToString();
            ImgHeader.Children.Add(tbMax);
            Canvas.SetRight(tbMax, 0.03 * w);
            Canvas.SetTop(tbMax, 0.45 * h);

            TextBlock tbup = new TextBlock();
            tbup.Text = "上";
            ImgHeader.Children.Add(tbup);
            Canvas.SetLeft(tbup, 0);
            Canvas.SetBottom(tbup, 0);
            
            TextBlock tbup2 = new TextBlock();
            tbup2.Text = "上";
            ImgHeader.Children.Add(tbup2);
            Canvas.SetRight(tbup2, 0);
            Canvas.SetBottom(tbup2, 0);

            TextBlock tbdown = new TextBlock();
            tbdown.Text = "下";
            ImgHeader.Children.Add(tbdown);
            Canvas.SetLeft(tbdown, 0.47 * w);
            Canvas.SetBottom(tbdown, 0);

            if (sector > 3)
            {
                TextBlock tbleft = new TextBlock();
                tbleft.Text = "右";
                ImgHeader.Children.Add(tbleft);
                Canvas.SetLeft(tbleft, 0.23 * w);
                Canvas.SetBottom(tbleft, 0);

                TextBlock tbright = new TextBlock();
                tbright.Text = "左";
                ImgHeader.Children.Add(tbright);
                Canvas.SetRight(tbright, 0.23 * w);
                Canvas.SetBottom(tbright, 0);

            }

        }

        private void DrawGammaImg()
        {
            if (!GammaImg || sector == 0)
            {
                return;
            }

            double h = ImgArea.ActualHeight;
            double w = ImgArea.ActualWidth;
           
            double yStep = h / cntYStep / YMajorGridInterval;

            var shows0 = GammaImgData.Where(o => o.depth >= YScaleMin - 0.1 && o.depth < YScaleMax).ToArray();
            int k = shows0.Count();
            List<GammaImgItem> shows = new List<GammaImgItem>();
            int itv = 0;
            if (k > MaxPoints)
            {
                itv = k / MaxPoints;
            }
            for (int i = 0; i < k; i++)
            {
                shows.Add(shows0[i]);
                i += itv;
            }

            //System.Diagnostics.Debug.WriteLine(k + "\t" + shows.Count());

            foreach (var item in shows)
            {
                Rectangle rectangle = new Rectangle();
                rectangle.Height = 0.1 * yStep * (itv + 1.05);
                rectangle.Width = w;
                Canvas.SetLeft(rectangle, 0);
                Canvas.SetTop(rectangle, (item.depth - YScaleMin) * yStep);

                LinearGradientBrush gradientBrush = new LinearGradientBrush();
                
                double min= ChartSettings.minGammaImg;
                double max= ChartSettings.maxGammaImg;
                double scale = max - min;
                int type = ChartSettings.ImgColorType;

                if (sector == 2)
                {
                    Color color1 = UniformedColor((item.UpGamma - min) / scale, type);
                    Color color2 = UniformedColor((item.DownGamma - min) / scale, type);
                    gradientBrush.GradientStops.Add(new GradientStop { Color = color1, Offset = 0 });
                    gradientBrush.GradientStops.Add(new GradientStop { Color = color2, Offset = 0.5 });
                    gradientBrush.GradientStops.Add(new GradientStop { Color = color1, Offset = 1 });
                }
                else
                {
                    Color color1 = UniformedColor((item.UpGamma - min) / scale, type);
                    Color color2 = UniformedColor((item.DownGamma - min) / scale, type);
                    Color color3 = UniformedColor((item.LeftGamma - min) / scale, type);
                    Color color4 = UniformedColor((item.RightGamma - min) / scale, type);

                    gradientBrush.GradientStops.Add(new GradientStop { Color = color1, Offset = 0 });
                    gradientBrush.GradientStops.Add(new GradientStop { Color = color4, Offset = 0.25 });
                    gradientBrush.GradientStops.Add(new GradientStop { Color = color2, Offset = 0.5 });
                    gradientBrush.GradientStops.Add(new GradientStop { Color = color3, Offset = 0.75 });
                    gradientBrush.GradientStops.Add(new GradientStop { Color = color1, Offset = 1 });

                }

                gradientBrush.StartPoint = new Point(0, 0.5);
                gradientBrush.EndPoint = new Point(1, 0.5);

                rectangle.Fill = gradientBrush;

                ImgArea.Children.Add(rectangle);
                
            }

        }

        private void Redraw()
        {
            CurveArea.Children.Clear();
            RulerArea.Children.Clear();
            ImgArea.Children.Clear();
            
            DrawGrid();
            DrawCurve();
            DrawGammaImg();
        }

        private void CurveArea_MouseWheel(object sender, MouseWheelEventArgs e)
        {
            if (!ChartUpdate)
                return;

            YScaleMax -= e.Delta * 0.015 * YGridInterval;
            YScaleMin -= e.Delta * 0.015 * YGridInterval;

            if (e.Delta > 0 && YScaleMin < MinDepth)
            {
                YScaleMin = MinDepth;
                YScaleMax = MinDepth + cntYStep * YMajorGridInterval;
            }
            if (e.Delta < 0 && YScaleMax > MaxDepth)
            {
                YScaleMax = MaxDepth;
                YScaleMin = YScaleMax - cntYStep * YMajorGridInterval;
            }

            if (YfirstVal < YScaleMin)
            {
                YfirstVal += YMajorGridInterval;
            }

            if (YfirstVal - YScaleMin > YMajorGridInterval)
            {
                YfirstVal -= YMajorGridInterval;
            }

            Redraw();
        }

        private void RulerArea_MouseWheel(object sender, MouseWheelEventArgs e)
        {
            if (!ChartUpdate)
                return;

            if (e.Delta > 0)
            {
                cntYStep--;
                if (cntYStep < 4)
                {
                    cntYStep = 7;
                    if (YMajorGridInterval == 5)
                    {
                        YMajorGridInterval = 4;
                    }

                    YMajorGridInterval /= 2;

                    if (YMajorGridInterval < 1)
                    {
                        YMajorGridInterval = 1;
                        cntYStep = 4;
                    }

                }
                if (YfirstVal - YScaleMin > YMajorGridInterval)
                {
                    YfirstVal -= YMajorGridInterval;
                }

            }
            else
            {
                cntYStep++;
                if (cntYStep > 7)
                {
                    cntYStep = 4;
                    YMajorGridInterval *= 2;
                    if (YMajorGridInterval == 4)
                    {
                        YMajorGridInterval = 5;
                    }
                }
                if (YScaleMin + cntYStep * YMajorGridInterval - YMajorGridInterval > MaxDepth)
                {
                    cntYStep--;
                    if (cntYStep < 4)
                    {
                        cntYStep = 7;
                        YMajorGridInterval /= 2;
                    }
                }
            }

            YScaleMax = YScaleMin + cntYStep * YMajorGridInterval;

            Redraw();
        }

        private void CurveArea_MouseRightButtonUp(object sender, MouseButtonEventArgs e)
        {
            if (!ChartUpdate)
                return;

            string text = "";

            UIElement line = null;
            UIElement tb = null;
            UIElement tbV = null;

            foreach (UIElement item in CurveArea.Children)
            {
                if (typeof(Line) == item.GetType())
                {
                    if (((Line)item).Tag != null)
                        line = item;
                }
                if (typeof(TextBlock) == item.GetType())
                {
                    if (((TextBlock)item).Tag != null)
                        tbV = item;
                }
            }
            if (line != null)
                CurveArea.Children.Remove(line);

            if (tbV != null)
                CurveArea.Children.Remove(tbV);

            foreach (UIElement item in RulerArea.Children)
            {
                if (typeof(TextBlock) == item.GetType())
                {
                    if (((TextBlock)item).Tag != null)
                        tb = item;
                }
            }
            if (line != null)
                CurveArea.Children.Remove(line);
            if (tb != null)
                RulerArea.Children.Remove(tb);

            Point point = e.GetPosition(CurveArea);

            double h = CurveArea.ActualHeight;
            double w = CurveArea.ActualWidth;

            Line hline = new Line();
            hline.Stroke = Brushes.Black;
            hline.StrokeDashArray = new DoubleCollection { 2, 2 };
            hline.StrokeThickness = 2;
            hline.X1 = 0;
            hline.X2 = w;
            hline.Y1 = point.Y;
            hline.Y2 = point.Y;
            hline.Tag = "H_Line";
            CurveArea.Children.Add(hline);

            double depth = point.Y / h * cntYStep * YMajorGridInterval + YScaleMin;

            foreach (var serie in Series)
            {
                double val = -999;
                var ss = serie.Values.FirstOrDefault((x) => x.Item1 >= depth);
                if (ss != null)
                    val = ss.Item2;
                text += serie.Title + ":" + val.ToString("0.0") + " ";
            }

            text = text.Substring(0, text.Length - 1);
            TextBlock tbValue = new TextBlock();
            tbValue.Text = text;
            tbValue.Foreground = new SolidColorBrush(Colors.DarkBlue);
            tbValue.FontSize = 12;
            tbValue.FontWeight = FontWeights.Black;
            tbValue.Tag = "Text";
            Canvas.SetRight(tbValue, 0);
            Canvas.SetTop(tbValue, point.Y - 20);

            CurveArea.Children.Add(tbValue);

            TextBlock textBlock = new TextBlock();
            textBlock.Text = depth.ToString("0.0");
            textBlock.Tag = "Text";
            Canvas.SetLeft(textBlock, 3);
            Canvas.SetTop(textBlock, point.Y - 5);

            RulerArea.Children.Add(textBlock);


        }

        private void UserControl_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            if (ChartUpdate)
            {
                CurveArea.Children.Clear();
                RulerArea.Children.Clear();
                HeaderArea.Children.Clear();
                ImgHeader.Children.Clear();
                ImgArea.Children.Clear();
                DrawGrid();
                DrawCurve();
                DrawGammaImg();
                DrawHeader();
                DrawImgHeader();
            }
        }

        /// <summary>
        /// 取色标颜色
        /// </summary>
        /// <param name="i"> 0 ~ 1 </param>
        /// <param name="type">默认为暗黑黄色</param>
        /// <returns></returns>
        private Color UniformedColor(double i, int type = 0)
        {
            long r = 0;
            long g = 0;
            long b = 0;

            if (type == 0)
            {
                if (i > 1)
                {
                    return Colors.Black;
                }
                if (i < 0)
                {
                    return Colors.White;
                }
                if (i < 0.25)
                {
                    b = Convert.ToInt64(255 - i * 1020);
                    g = Convert.ToInt64(255 - i * 160);
                    r = 255;
                }
                else if (i < 0.5)
                {
                    b = Convert.ToInt64((i - 0.25) * 120);
                    g = Convert.ToInt64(215 - (i - 0.25) * 440);
                    r = Convert.ToInt64(255 - (i - 0.25) * 180);
                }
                else if (i < 0.75)
                {
                    b = Convert.ToInt64(30 - (i - 0.5) * 120);
                    g = Convert.ToInt64(105 - (i - 0.5) * 420);
                    r = Convert.ToInt64(210 - (i - 0.5) * 284);
                }
                else
                {
                    b = 0;
                    g = 0;
                    r = Convert.ToInt64(139 - (i - 0.5) * 556);
                }

            }
            else if (type == 1)
            {
                if (i > 1)
                {
                    return Colors.Red;
                }
                if (i < 0)
                {
                    return Colors.Blue;
                }

                if (i < 0.25)
                {
                    b = 255;
                    g = Convert.ToInt64(i * 1020);
                    r = 0;
                }
                else if (i < 0.5)
                {
                    b = Convert.ToInt64(255 - (i - 0.25) * 1020);
                    g = 255;
                    r = 0;
                }
                else if (i < 0.75)
                {
                    b = 0;
                    g = 255;
                    r = Convert.ToInt64((i - 0.5) * 1020);
                }
                else
                {
                    b = 0;
                    g = Convert.ToInt64(255 - (i - 0.75) * 1020);
                    r = 255;
                }
            }
            else
            {
                  
            }           

            if (b < 0)
            {
                b = 0;
            }
            if (b > 255)
            {
                b = 255;
            }
            if (r < 0)
            {
                r = 0;
            }
            if (r > 255)
            {
                r = 255;
            }
            if (g < 0)
            {
                g = 0;
            }
            if (g > 255)
            {
                g = 255;
            }

            return Color.FromRgb((byte)r, (byte)g, (byte)b);
           
        }

        private class GammaImgItem
        {
            public double depth;
            public double UpGamma;
            public double DownGamma;
            public double LeftGamma;
            public double RightGamma;
        }
    }
   
    
}

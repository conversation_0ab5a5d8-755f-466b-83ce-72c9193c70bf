﻿<UserControl x:Class="TartanLogApp.CurveControl.LogCurveControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"             
             mc:Ignorable="d" MinHeight="600" 
             d:DesignHeight="600" d:DesignWidth="360" SizeChanged="UserControl_SizeChanged">
    <Border BorderBrush="Black" BorderThickness="1" >
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="2*" />
                <RowDefinition Height="9*" />
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="1*"/>
                <ColumnDefinition Width="6*"/>
            </Grid.ColumnDefinitions>
            <Canvas Background="White">
                <!--<Path Fill="#004F85" Data="M17.6,0 L17.6,12.3 17.6,59.3 32.8,59.3 32.8,12.3 50.5,12.3 50.5,0 z"/>
                <Path Fill="#9CA3AA" Data="M0,12.3 L12.3,12.3 12.3,0 0,0 z"/>-->

                <Path Fill="#FF004F85" Data="M23,10 L23,14.9 23,33.7 29.1,33.7 29.1,14.9 36.2,14.9 36.2,10"/>
                <Path Fill="#FF9CA3AA" Data="M16,14.9 L20.9,14.9 20.9,10 16,10"/>
                <TextBlock Text="井" FontSize="14" Canvas.Left="18" Canvas.Top="60"/>
                <TextBlock Text="深" FontSize="14" Canvas.Left="18" Canvas.Top="80"/>
                <TextBlock Text="m" FontSize="14" Canvas.Left="18" Canvas.Top="100"/>
            </Canvas>
            <Canvas x:Name="RulerArea" MouseWheel="RulerArea_MouseWheel" Background="White" Grid.Row="1" >

            </Canvas>

            <Border BorderBrush="Black" BorderThickness="2,0,0,1" Grid.Column="1">
                <Canvas x:Name="HeaderArea" Background="White" >

                    <Line Stroke="#FFE81E63" X1="10" Y1="45" X2="168" Y2="45"></Line>

                    <!--<Path Stroke="#FFFEC007" Data="M 1,0 L 2,1  1,2  0,1 z">  </Path>-->
                </Canvas>
            </Border>
            <Border BorderBrush="Black" BorderThickness="2,1,0,0" Grid.Row="1" Grid.Column="1">
                <Canvas x:Name="CurveArea" ClipToBounds="True" Background="White" MouseWheel="CurveArea_MouseWheel" MouseRightButtonUp="CurveArea_MouseRightButtonUp" >
                    <Rectangle Fill ="#FF2D89EF" Canvas.Left="5" Canvas.Top="10" Width="30" Height="20" ></Rectangle>
                    <Rectangle Fill ="#FFEE1111" Canvas.Left="40" Canvas.Top="10" Width="30" Height="20" ></Rectangle>
                    <Rectangle Fill ="#FFFFC40D" Canvas.Left="75" Canvas.Top="10"  Width="30" Height="20" ></Rectangle>
                    <Rectangle Fill ="#FF00ABA9" Canvas.Left="110" Canvas.Top="10" Width="30" Height="20" ></Rectangle>
                    <Rectangle Fill ="#FFFF0097" Canvas.Left="145" Canvas.Top="10"  Width="30" Height="20" ></Rectangle>

                    <Rectangle Fill ="#FF00A300" Canvas.Left="5" Canvas.Top="40" Width="30" Height="20" ></Rectangle>
                    <Rectangle Fill ="#FFDA532C" Canvas.Left="40" Canvas.Top="40" Width="30" Height="20" ></Rectangle>
                    <Rectangle Fill ="#FF2B5797" Canvas.Left="75" Canvas.Top="40"  Width="30" Height="20" ></Rectangle>
                    <Rectangle Fill ="#FF00FF80" Canvas.Left="110" Canvas.Top="40" Width="30" Height="20" ></Rectangle>
                    <Rectangle Fill ="#FF004F85" Canvas.Left="145" Canvas.Top="40"  Width="30" Height="20" ></Rectangle>
                </Canvas>
            </Border>
        </Grid>
    </Border>
</UserControl>

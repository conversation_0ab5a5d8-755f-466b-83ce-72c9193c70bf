﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace TartanLogApp.CurveControl
{
    /// <summary>
    /// ChartSettingWindow.xaml 的交互逻辑
    /// </summary>
    public partial class ChartSettingWindow : Window
    {
        public ChartSettingWindow()
        {
            InitializeComponent();
        }

        private void tbOK_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ChartSettings.minDepth = double.Parse(tbMinDepth.Text.Trim());
                ChartSettings.minGammaImg = double.Parse(tbMinImg.Text.Trim());
                ChartSettings.maxGammaImg = double.Parse(tbMaxImg.Text.Trim());

                int k = ChartSettings.serieSettings.Count;
                if (k > 0)
                {                   
                    ChartSettings.serieSettings[0].minVal = double.Parse(tbMinGV1.Text.Trim());
                    ChartSettings.serieSettings[0].maxVal = double.Parse(tbMaxGV1.Text.Trim());
                   
                    if (k > 1)
                    {

                        ChartSettings.serieSettings[1].minVal = double.Parse(tbMinGV2.Text.Trim());
                        ChartSettings.serieSettings[1].maxVal = double.Parse(tbMaxGV2.Text.Trim());

                        if (k > 2)
                        {
                            ChartSettings.serieSettings[2].minVal = double.Parse(tbMinGV3.Text.Trim());
                            ChartSettings.serieSettings[2].maxVal = double.Parse(tbMaxGV3.Text.Trim());
                            if (k > 3)
                            {
                                ChartSettings.serieSettings[3].minVal = double.Parse(tbMinGV4.Text.Trim());
                                ChartSettings.serieSettings[3].maxVal = double.Parse(tbMaxGV4.Text.Trim());
                            }

                        }

                    }
                }

            }
            catch (Exception)
            {
                MessageBox.Show("输入参数不正确！");
                return ;
            }            

            ChartSettings.set = true;
            DialogResult = true;
        }

        private void tbCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
        }

        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            tbMinDepth.Text = ChartSettings.minDepth.ToString();
            tbMinImg.Text = ChartSettings.minGammaImg.ToString();
            tbMaxImg.Text = ChartSettings.maxGammaImg.ToString();

            int k = ChartSettings.serieSettings.Count;
            if (k > 0)
            {
                panel1.Visibility = Visibility.Visible;
                lbGV1.Text = ChartSettings.serieSettings[0].grName;
                tbMinGV1.Text = ChartSettings.serieSettings[0].minVal.ToString();
                tbMaxGV1.Text = ChartSettings.serieSettings[0].maxVal.ToString();

                if (k > 1)
                {

                    panel2.Visibility = Visibility.Visible;
                    lbGV2.Text = ChartSettings.serieSettings[1].grName;
                    tbMinGV2.Text = ChartSettings.serieSettings[1].minVal.ToString();
                    tbMaxGV2.Text = ChartSettings.serieSettings[1].maxVal.ToString();

                    if (k > 2)
                    {
                        panel3.Visibility = Visibility.Visible;
                        lbGV3.Text = ChartSettings.serieSettings[2].grName;
                        tbMinGV3.Text = ChartSettings.serieSettings[2].minVal.ToString();
                        tbMaxGV3.Text = ChartSettings.serieSettings[2].maxVal.ToString();
                        if (k > 3)
                        {
                            panel4.Visibility = Visibility.Visible;
                            lbGV4.Text = ChartSettings.serieSettings[3].grName;
                            tbMinGV4.Text = ChartSettings.serieSettings[3].minVal.ToString();
                            tbMaxGV4.Text = ChartSettings.serieSettings[3].maxVal.ToString();
                        }

                    }

                }
            }           
        }
    }
   
    public static class ChartSettings
    {
        public static bool set = false;
        public static double minDepth;
        public static List<SerieSettings> serieSettings = new List<SerieSettings>();
        public static int ImgColorType;
        public static double minGammaImg;
        public static double maxGammaImg;

        public static bool IsGammaSetting => maxGammaImg - minGammaImg > 0;

        public static void Reset()
        {
            set = false;
            minDepth = 0;
            minGammaImg = 0;
            maxGammaImg = 0;
            ImgColorType = 0;
            serieSettings.Clear();
        }
    }
    public class SerieSettings
    {
        public string grName;
        public Brush stroke;
        public double minVal;
        public double maxVal;
    }

}

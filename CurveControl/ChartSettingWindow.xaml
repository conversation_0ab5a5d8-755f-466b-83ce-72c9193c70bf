﻿<Window x:Class="TartanLogApp.CurveControl.ChartSettingWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:TartanLogApp.CurveControl"
        mc:Ignorable="d" WindowStyle="None" ResizeMode="NoResize" 
        BorderThickness="1" BorderBrush="LightBlue" Background="AliceBlue" 
        Loaded="Window_Loaded"
        Title="ChartSettingWindow" Height="265" Width="500">
    <Grid>
        <Grid.RowDefinitions >
            <RowDefinition Height="100"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        <StackPanel>
            <TextBlock Text="显示设置" FontSize="18" FontWeight="Black"  Margin="10"/>
            <Line X1="10" Y1="7" X2="480" Y2="7" Stroke="DarkGray" StrokeThickness="1"/>
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="起始井深" FontSize="16" Margin="10"/>              
                <TextBox x:Name="tbMinDepth" Width="100" Height="30" FontSize="16" Padding="5" VerticalContentAlignment="Center"/>
                
                <TextBlock Text="方位成像" FontSize="16" Margin="45,10,10,0"/>
                <TextBox x:Name="tbMinImg" Width="60" Height="30" FontSize="16" VerticalContentAlignment="Center" />
                <TextBlock Text=" - " FontSize="16" Margin="10"/>
                <TextBox x:Name="tbMaxImg" Width="60" Height="30" FontSize="16" VerticalContentAlignment="Center" />
            </StackPanel>
            <Line X1="10" Y1="5" X2="480" Y2="5" Stroke="DarkGray" StrokeThickness="1"/>
        </StackPanel>
        <StackPanel Grid.Row="1">
            <StackPanel Orientation="Horizontal">
                <StackPanel x:Name="panel1" Visibility="Hidden" Orientation="Horizontal" Margin="5">
                    <TextBlock x:Name="lbGV1" Text="GV1" FontSize="16" Margin="10"/>
                    <TextBox x:Name="tbMinGV1" Width="60" Height="30" FontSize="16" VerticalContentAlignment="Center" />
                    <TextBlock Text=" - " FontSize="16" Margin="10"/>
                    <TextBox x:Name="tbMaxGV1" Width="60" Height="30" FontSize="16" VerticalContentAlignment="Center" />
                </StackPanel>
                <StackPanel x:Name="panel2" Visibility="Hidden"  Orientation="Horizontal" Margin="30,5">
                    <TextBlock x:Name="lbGV2" Text="GV2" FontSize="16" Margin="10"/>
                    <TextBox x:Name="tbMinGV2" Width="60" Height="30" FontSize="16" VerticalContentAlignment="Center" />
                    <TextBlock Text=" - " FontSize="16" Margin="10"/>
                    <TextBox x:Name="tbMaxGV2" Width="60" Height="30" FontSize="16" VerticalContentAlignment="Center" />
                </StackPanel>
            </StackPanel>
            <StackPanel Orientation="Horizontal">
                <StackPanel x:Name="panel3" Visibility="Hidden"  Orientation="Horizontal" Margin="5">
                    <TextBlock x:Name="lbGV3" Text="GV3" FontSize="16" Margin="10"/>
                    <TextBox x:Name="tbMinGV3" Width="60" Height="30" FontSize="16" VerticalContentAlignment="Center" />
                    <TextBlock Text=" - " FontSize="16" Margin="10"/>
                    <TextBox x:Name="tbMaxGV3" Width="60" Height="30" FontSize="16" VerticalContentAlignment="Center" />
                </StackPanel>
                <StackPanel x:Name="panel4" Visibility="Hidden"  Orientation="Horizontal" Margin="30,5">
                    <TextBlock x:Name="lbGV4" Text="GV4" FontSize="16" Margin="10"/>
                    <TextBox x:Name="tbMinGV4" Width="60" Height="30" FontSize="16" VerticalContentAlignment="Center" />
                    <TextBlock Text=" - " FontSize="16" Margin="10"/>
                    <TextBox x:Name="tbMaxGV4" Width="60" Height="30" FontSize="16" VerticalContentAlignment="Center" />
                </StackPanel>
            </StackPanel>
            <Line X1="10" Y1="5" X2="480" Y2="5" Stroke="DarkGray" StrokeThickness="1"/>
        </StackPanel>
        
        <Grid Grid.Row="2">
            <Button x:Name="tbOK" Background="LightSkyBlue" Content="确定" Width="80" Height="35" HorizontalAlignment="Left" Margin="150,10,0,10" Click="tbOK_Click"/>
            <Button x:Name="tbCancel" Content="取消" Background="BlanchedAlmond" Width="80" Height="35" HorizontalAlignment="Left" Margin="260,10,0,10" Click="tbCancel_Click"/>
        </Grid>
    </Grid>
</Window>

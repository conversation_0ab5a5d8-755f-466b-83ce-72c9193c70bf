# Apple风格暗色模式设计指南实现

## 概述

本项目已完全按照Apple Human Interface Guidelines的暗色模式设计原则进行了优化，提供了更好的可访问性、视觉舒适度和用户体验。

## 设计原则

### 1. 颜色系统
遵循Apple的语义化颜色系统，确保在暗色环境下的最佳可读性和对比度：

#### 背景颜色层次
- **System Background** (`#000000`): 主背景色，提供最深的背景层次
- **Secondary System Background** (`#1C1C1E`): 次要背景色，用于容器和卡片
- **Tertiary System Background** (`#2C2C2E`): 三级背景色，用于提升的元素

#### 分隔符颜色
- **Separator** (`#38383A`): 标准分隔符，用于边框和分割线
- **Opaque Separator** (`#48484A`): 不透明分隔符，用于更明显的分割

#### 文字颜色层次
- **Label** (`#FFFFFF`): 主标签色，用于最重要的文字内容
- **Secondary Label** (`rgba(235, 235, 245, 0.6)`): 次要标签色，60%透明度
- **Tertiary Label** (`rgba(235, 235, 245, 0.3)`): 三级标签色，30%透明度
- **Quaternary Label** (`#8E8E93`): 四级标签色，用于占位符等

### 2. 系统色彩
使用Apple推荐的系统色彩，确保在暗色模式下的高可见性：

- **System Blue** (`#0A84FF`): 主色调，比标准蓝色更亮
- **System Green** (`#32D74B`): 成功状态色
- **System Orange** (`#FF9F0A`): 警告状态色
- **System Red** (`#FF453A`): 错误状态色
- **System Purple** (`#BF5AF2`): 强调色

### 3. 交互状态
针对暗色模式优化的交互反馈：

- **Hover**: `rgba(255, 255, 255, 0.08)` - 悬停状态
- **Active**: `rgba(255, 255, 255, 0.12)` - 激活状态
- **Focus**: `rgba(10, 132, 255, 0.3)` - 焦点状态
- **Disabled**: `rgba(235, 235, 245, 0.3)` - 禁用状态

## 实现细节

### 1. 主题配置 (`theme.ts`)
- 更新了暗色主题的所有颜色值
- 优化了组件级别的样式配置
- 增强了阴影效果以提供更好的层次感

### 2. CSS样式优化 (`modern.css` & `apple-dark-mode.css`)
- 为所有Ant Design组件添加了暗色模式样式
- 使用CSS变量系统便于维护和扩展
- 优化了滚动条、按钮、输入框等组件的视觉效果

### 3. 组件适配
- 所有UI组件都已适配暗色模式
- 确保文字对比度符合可访问性标准
- 优化了交互状态的视觉反馈

## 可访问性改进

### 1. 对比度优化
- 主要文字使用纯白色 (`#FFFFFF`)，确保最高对比度
- 次要文字使用适当的透明度，保持层次感
- 所有颜色组合都符合WCAG 2.1 AA标准

### 2. 视觉舒适度
- 使用纯黑背景减少眼部疲劳
- 优化了阴影效果，在暗色环境下提供更好的深度感知
- 渐变效果更加柔和，避免过度刺激

### 3. 交互反馈
- 增强了悬停和焦点状态的视觉反馈
- 使用Apple系统蓝色作为焦点指示器
- 优化了按钮和输入框的交互状态

## 组件覆盖

已优化的组件包括：
- ✅ 按钮 (Button)
- ✅ 输入框 (Input, Select, DatePicker)
- ✅ 卡片 (Card)
- ✅ 表格 (Table)
- ✅ 标签 (Tag)
- ✅ 徽章 (Badge)
- ✅ 工具提示 (Tooltip)
- ✅ 消息提示 (Message)
- ✅ 选择器 (Select)
- ✅ 模态框 (Modal)
- ✅ 抽屉 (Drawer)
- ✅ 菜单 (Menu)
- ✅ 标签页 (Tabs)
- ✅ 折叠面板 (Collapse)
- ✅ 步骤条 (Steps)
- ✅ 面包屑 (Breadcrumb)
- ✅ 进度条 (Progress)
- ✅ 评分 (Rate)
- ✅ 滑块 (Slider)
- ✅ 开关 (Switch)
- ✅ 上传 (Upload)
- ✅ 树形控件 (Tree)
- ✅ 穿梭框 (Transfer)
- ✅ 锚点 (Anchor)
- ✅ 回到顶部 (BackTop)
- ✅ 空状态 (Empty)
- ✅ 结果页 (Result)

## 演示页面

创建了专门的暗色模式演示页面 (`DarkModeDemo.tsx`)，展示了：
- 所有组件在暗色模式下的效果
- Apple风格的设计实现
- 交互状态的视觉反馈
- 可访问性特性

## 文字可见性问题修复

### 已修复的问题
1. **主标题可见性**: 修复了"达坦实时Gamma成像系统"标题在暗色模式下的显示问题
2. **侧边栏标题**: 修复了"达坦系统"和"Gamma成像系统"文字的可见性
3. **状态文字**: 修复了各面板中状态信息文字的显示问题，包括：
   - ImagingSettingsPanel中的"当前模式"、"方向配置"、"就绪"状态
   - GammaSettingsPanel中的状态信息区域文字
   - JobSettingsPanel中的井号显示文字

### 修复方法
1. **组件级修复**: 直接在组件中使用`dark:text-white`类名
2. **CSS强制规则**: 在`apple-dark-mode.css`和`modern.css`中添加高优先级的文字颜色规则
3. **Typography组件优化**: 确保所有Ant Design Typography组件在暗色模式下使用正确的颜色
4. **状态组件修复**: 优化StatusIndicator等状态显示组件的文字颜色

## 使用方法

1. 点击右上角的主题切换按钮在亮色和暗色模式间切换
2. 访问"暗色模式演示"页面查看所有组件效果
3. 访问"文字可见性测试"页面验证所有文字的可读性
4. 主题偏好会自动保存到localStorage

## 技术特性

- 🎨 完全遵循Apple Human Interface Guidelines
- 🌙 优雅的暗色模式实现
- ♿ 符合WCAG 2.1 AA可访问性标准
- 🎯 高对比度文字确保可读性
- 💫 流畅的主题切换动画
- 💾 主题偏好本地存储
- 📱 响应式设计适配

## 开发建议

1. **保持一致性**: 新增组件时请遵循相同的颜色系统
2. **测试可访问性**: 确保新的颜色组合符合对比度要求
3. **使用CSS变量**: 利用定义的CSS变量保持样式一致性
4. **考虑交互状态**: 为所有交互元素提供适当的视觉反馈

## 参考资源

- [Apple Human Interface Guidelines - Dark Mode](https://developer.apple.com/design/human-interface-guidelines/dark-mode)
- [Apple Design Resources](https://developer.apple.com/design/resources/)
- [WCAG 2.1 Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)

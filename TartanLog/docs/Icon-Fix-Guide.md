# Ant Design 图标修复指南

## 问题描述

在从shadcn/ui转换到Ant Design时，经常会遇到图标不存在的问题，比如：
```
SyntaxError: Importing binding name 'ActivityOutlined' is not found.
```

## 常见问题图标及其替代方案

### 1. ActivityOutlined → LineChartOutlined
**问题**: `ActivityOutlined` 在Ant Design中不存在
**解决方案**: 使用 `LineChartOutlined` 替代

```typescript
// ❌ 错误
import { ActivityOutlined } from '@ant-design/icons';

// ✅ 正确
import { LineChartOutlined } from '@ant-design/icons';
```

### 2. 其他常见的图标映射

| shadcn/ui 概念 | Ant Design 图标 | 用途 |
|---------------|----------------|------|
| Activity | LineChartOutlined | 活动/图表相关 |
| Database | DatabaseOutlined | 数据库相关 |
| Settings | SettingOutlined | 设置相关 |
| Play | PlayCircleOutlined | 播放/开始 |
| Square | PauseOutlined | 停止/暂停 |
| Upload | UploadOutlined | 上传相关 |
| Trash2 | DeleteOutlined | 删除相关 |

### 3. 如何查找正确的图标

#### 方法1: 官方文档
访问 [Ant Design Icons](https://ant.design/components/icon) 查看所有可用图标

#### 方法2: 在线搜索
在Ant Design图标页面使用搜索功能查找相似的图标

#### 方法3: 常用图标分类
- **操作类**: PlayCircleOutlined, PauseOutlined, StopOutlined
- **文件类**: UploadOutlined, DownloadOutlined, ExportOutlined
- **编辑类**: EditOutlined, DeleteOutlined, CopyOutlined
- **状态类**: CheckCircleOutlined, CloseCircleOutlined, ExclamationCircleOutlined
- **导航类**: HomeOutlined, MenuOutlined, ArrowLeftOutlined
- **数据类**: DatabaseOutlined, TableOutlined, LineChartOutlined

## 修复步骤

### 1. 识别错误
当遇到图标导入错误时，错误信息会明确指出哪个图标不存在：
```
SyntaxError: Importing binding name 'ActivityOutlined' is not found.
```

### 2. 查找替代图标
根据图标的用途，在Ant Design图标库中查找相似的图标

### 3. 替换导入语句
```typescript
// 修改前
import { ActivityOutlined } from '@ant-design/icons';

// 修改后
import { LineChartOutlined } from '@ant-design/icons';
```

### 4. 替换使用位置
```typescript
// 修改前
<ActivityOutlined className="mr-2" />

// 修改后
<LineChartOutlined className="mr-2" />
```

## 预防措施

### 1. 使用TypeScript
TypeScript会在编译时检查图标是否存在，帮助提前发现问题

### 2. 参考官方文档
在选择图标时，优先参考Ant Design官方文档中的图标列表

### 3. 建立图标映射表
为项目建立一个图标映射表，记录常用的图标对应关系

## 本项目中的修复记录

### GammaImagingSystem.tsx
- `ActivityOutlined` → `LineChartOutlined`
  - 用于实时Gamma曲线的图标显示
  - 用于曲线显示区域的占位图标

## 验证修复

修复完成后，可以通过以下方式验证：

1. **编译检查**: 确保没有TypeScript编译错误
2. **运行时检查**: 启动开发服务器，确保没有运行时错误
3. **视觉检查**: 在浏览器中查看图标是否正确显示

## 总结

图标问题是从其他UI库迁移到Ant Design时的常见问题，但通过：
- 仔细阅读错误信息
- 查找合适的替代图标
- 系统性地替换所有使用位置

可以快速解决这类问题。建议在项目开发过程中建立图标使用规范，避免类似问题的重复出现。

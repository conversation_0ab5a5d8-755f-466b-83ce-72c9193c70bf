{"$schema": "https://schema.tauri.app/config/2", "productName": "tartanlog", "version": "0.1.0", "identifier": "com.tartanlog.app", "build": {"beforeDevCommand": "bun run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "bun run build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "tartanlog", "width": 1300, "height": 860}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}
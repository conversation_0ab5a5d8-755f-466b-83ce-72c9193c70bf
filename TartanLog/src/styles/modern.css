/* 现代化UI样式增强 */

/* 全局样式优化 */
* {
  box-sizing: border-box;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 暗色模式滚动条 - 遵循Apple设计指南的滚动条样式 */
.dark ::-webkit-scrollbar-track {
  background: #2C2C2E; /* Tertiary System Background */
}

.dark ::-webkit-scrollbar-thumb {
  background: #48484A; /* Opaque Separator */
  transition: background 0.2s ease;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #636366; /* Placeholder Text */
}

/* 现代化按钮增强 */
.ant-btn {
  border-radius: 8px !important;
  font-weight: 500;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1) !important;
}

.ant-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1) !important;
}

.ant-btn-primary {
  background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%) !important;
  border: none !important;
}

.ant-btn-primary:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 100%) !important;
}

/* 暗色模式按钮样式 - 遵循Apple设计指南 */
.dark .ant-btn {
  background: #1C1C1E !important; /* Secondary System Background */
  border-color: #38383A !important; /* Separator */
  color: #FFFFFF !important; /* Label */
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.8), 0 1px 2px -1px rgba(0, 0, 0, 0.6) !important;
}

.dark .ant-btn:hover {
  background: #2C2C2E !important; /* Tertiary System Background */
  border-color: #48484A !important; /* Opaque Separator */
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.8), 0 2px 4px -2px rgba(0, 0, 0, 0.6) !important;
}

.dark .ant-btn-primary {
  background: linear-gradient(135deg, #0A84FF 0%, #64D2FF 100%) !important; /* Apple系统蓝色渐变 */
  border: none !important;
  color: #FFFFFF !important;
}

.dark .ant-btn-primary:hover {
  background: linear-gradient(135deg, #007AFF 0%, #0A84FF 100%) !important;
}

/* 现代化输入框 */
.ant-input,
.ant-select-selector {
  border-radius: 8px !important;
  border: 1px solid #e2e8f0 !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.ant-input:focus,
.ant-select-focused .ant-select-selector {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

/* 暗色模式输入框样式 - 遵循Apple设计指南 */
.dark .ant-input,
.dark .ant-input-number,
.dark .ant-select-selector {
  background: #1C1C1E !important; /* Secondary System Background */
  border-color: #38383A !important; /* Separator */
  color: #FFFFFF !important; /* Label */
}

.dark .ant-input:focus,
.dark .ant-input-number:focus,
.dark .ant-select-focused .ant-select-selector {
  border-color: #0A84FF !important; /* Apple系统蓝色 */
  box-shadow: 0 0 0 3px rgba(10, 132, 255, 0.3) !important;
  background: #2C2C2E !important; /* Tertiary System Background */
}

.dark .ant-input::placeholder,
.dark .ant-select-selection-placeholder {
  color: #8E8E93 !important; /* Quaternary Label */
}

/* 现代化卡片 */
.ant-card {
  border-radius: 16px !important;
  border: 1px solid #e2e8f0 !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.ant-card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1) !important;
}

.ant-card-head {
  border-bottom: 1px solid #f1f5f9 !important;
  border-radius: 16px 16px 0 0 !important;
}

/* 现代化表格 */
.ant-table {
  border-radius: 12px !important;
  overflow: hidden !important;
}

.ant-table-thead > tr > th {
  background: #f8fafc !important;
  border-bottom: 1px solid #e2e8f0 !important;
  font-weight: 600 !important;
  color: #374151 !important;
}

.ant-table-tbody > tr:hover > td {
  background: #f8fafc !important;
}

/* 暗色模式表格样式 - 遵循Apple设计指南 */
.dark .ant-table {
  background: #1C1C1E !important; /* Secondary System Background */
  border-color: #38383A !important; /* Separator */
}

.dark .ant-table-thead > tr > th {
  background: #2C2C2E !important; /* Tertiary System Background */
  border-bottom-color: #38383A !important; /* Separator */
  color: #FFFFFF !important; /* Label */
}

.dark .ant-table-tbody > tr > td {
  background: #1C1C1E !important; /* Secondary System Background */
  border-bottom-color: #38383A !important; /* Separator */
  color: #FFFFFF !important; /* Label */
}

.dark .ant-table-tbody > tr:hover > td {
  background: #2C2C2E !important; /* Tertiary System Background */
}

.dark .ant-table-tbody > tr.ant-table-row-selected > td {
  background: rgba(10, 132, 255, 0.2) !important; /* 选中行背景 */
}

/* 现代化标签 */
.ant-tag {
  border-radius: 6px !important;
  border: none !important;
  font-weight: 500 !important;
  padding: 2px 8px !important;
}

/* 现代化徽章 */
.ant-badge-status-dot {
  width: 8px !important;
  height: 8px !important;
}

/* 现代化分割线 */
.ant-divider {
  border-color: #f1f5f9 !important;
}

/* 现代化选择器 */
.ant-select-dropdown {
  border-radius: 8px !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1) !important;
}

.ant-select-item {
  border-radius: 4px !important;
  margin: 2px 4px !important;
}

/* 现代化工具提示 */
.ant-tooltip-inner {
  border-radius: 6px !important;
  background: #1e293b !important;
  font-size: 12px !important;
}

/* 现代化消息提示 */
.ant-message-notice {
  border-radius: 8px !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1) !important;
}

/* 暗色模式卡片样式 - 遵循Apple设计指南 */
.dark .ant-card {
  background: #1C1C1E !important; /* Secondary System Background */
  border-color: #38383A !important; /* Separator */
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.8), 0 1px 3px -1px rgba(0, 0, 0, 0.6) !important;
}

.dark .ant-card:hover {
  background: #2C2C2E !important; /* Tertiary System Background */
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.9), 0 2px 6px -2px rgba(0, 0, 0, 0.7) !important;
}

.dark .ant-card-head {
  background: #1C1C1E !important; /* Secondary System Background */
  border-bottom-color: #38383A !important; /* Separator */
}

.dark .ant-card-head-title {
  color: #FFFFFF !important; /* Label */
}

.dark .ant-card-body {
  color: #FFFFFF !important; /* Label */
}

.dark .ant-table-thead > tr > th {
  background: #2C2C2E !important;
  border-bottom-color: #38383A !important;
  color: #FFFFFF !important;
}

.dark .ant-table-tbody > tr:hover > td {
  background: #2C2C2E !important;
}

.dark .ant-input,
.dark .ant-select-selector {
  background: #2C2C2E !important;
  border-color: #38383A !important;
  color: #FFFFFF !important;
}

.dark .ant-divider {
  border-color: #38383A !important;
}

/* 确保暗色模式下文字有足够对比度 */
.dark .ant-typography,
.dark .ant-typography h1,
.dark .ant-typography h2,
.dark .ant-typography h3,
.dark .ant-typography h4,
.dark .ant-typography h5,
.dark .ant-typography h6,
.dark .ant-typography-title,
.dark .ant-typography-title h1,
.dark .ant-typography-title h2,
.dark .ant-typography-title h3,
.dark .ant-typography-title h4,
.dark .ant-typography-title h5,
.dark .ant-typography-title h6 {
  color: #FFFFFF !important;
}

/* 卡片标题 */
.dark .ant-card-head-title,
.dark .ant-card-head-title span {
  color: #FFFFFF !important;
}

/* 表单标签 */
.dark .ant-form-item-label > label {
  color: #FFFFFF !important;
}

/* 选择器 */
.dark .ant-select-selection-item {
  color: #FFFFFF !important;
}

/* 输入框占位符 */
.dark .ant-input::placeholder {
  color: #8E8E93 !important;
}

/* 表格内容 */
.dark .ant-table-tbody > tr > td {
  color: #FFFFFF !important;
}

/* 通用文字颜色强制覆盖 - 最高优先级 */
.dark,
.dark *:not(.ant-input):not(.ant-select-selection-search-input),
.dark span:not(.ant-input):not(.ant-select-selection-search-input),
.dark div:not(.ant-input):not(.ant-select-selection-search-input),
.dark p,
.dark label,
.dark h1,
.dark h2,
.dark h3,
.dark h4,
.dark h5,
.dark h6,
.dark strong,
.dark b {
  color: #FFFFFF !important;
}

/* Ant Design 特定组件强制白色文字 */
.dark .ant-typography,
.dark .ant-typography *,
.dark .ant-card-head-title,
.dark .ant-card-head-title *,
.dark .ant-form-item-label,
.dark .ant-form-item-label *,
.dark .ant-btn,
.dark .ant-btn *,
.dark .ant-badge-status-text,
.dark .ant-badge-status-text *,
.dark .ant-space-item,
.dark .ant-space-item * {
  color: #FFFFFF !important;
}

/* 次要文字颜色 */
.dark .text-secondary {
  color: #EBEBF599 !important;
}

/* 三级文字颜色 */
.dark .text-tertiary {
  color: #EBEBF54D !important;
}

/* 占位符和禁用状态保持灰色 */
.dark .ant-input::placeholder,
.dark .ant-select-selection-placeholder {
  color: #8E8E93 !important;
}

/* 覆盖Tailwind CSS的文字颜色类 */
.dark .text-slate-800,
.dark .text-slate-600,
.dark .text-slate-500,
.dark .text-slate-400 {
  color: #FFFFFF !important;
}

/* 确保自定义类名的文字也是白色 */
.dark .font-semibold,
.dark .font-bold {
  color: #FFFFFF !important;
}

/* 动画增强 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.3s ease-out;
}

/* 渐变背景 */
.gradient-bg {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.dark .gradient-bg {
  background: linear-gradient(135deg, #000000 0%, #1C1C1E 100%);
}

/* 玻璃效果 */
.glass-effect {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass-effect {
  background: rgba(28, 28, 30, 0.8);
  border: 1px solid rgba(56, 56, 58, 0.3);
}

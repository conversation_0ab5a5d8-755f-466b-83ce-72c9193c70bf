/* Apple风格暗色模式样式 - 遵循Apple Human Interface Guidelines */

/* 
  Apple暗色模式颜色系统：
  - System Background: #000000 (主背景)
  - Secondary System Background: #1C1C1E (次要背景)
  - Tertiary System Background: #2C2C2E (三级背景)
  - Separator: #38383A (分隔符)
  - Opaque Separator: #48484A (不透明分隔符)
  - Label: #FFFFFF (主标签)
  - Secondary Label: #EBEBF599 (次要标签，60%透明度)
  - Tertiary Label: #EBEBF54D (三级标签，30%透明度)
  - Quaternary Label: #8E8E93 (四级标签)
  - Placeholder Text: #636366 (占位符文字)
*/

/* 根元素暗色模式变量 */
.dark {
  /* Apple系统颜色变量 */
  --apple-system-background: #000000;
  --apple-secondary-background: #1C1C1E;
  --apple-tertiary-background: #2C2C2E;
  --apple-separator: #38383A;
  --apple-opaque-separator: #48484A;
  --apple-label: #FFFFFF;
  --apple-secondary-label: rgba(235, 235, 245, 0.6);
  --apple-tertiary-label: rgba(235, 235, 245, 0.3);
  --apple-quaternary-label: #8E8E93;
  --apple-placeholder-text: #636366;
  
  /* Apple系统色彩 */
  --apple-blue: #0A84FF;
  --apple-green: #32D74B;
  --apple-orange: #FF9F0A;
  --apple-red: #FF453A;
  --apple-purple: #BF5AF2;
  --apple-cyan: #64D2FF;
  
  /* 交互状态 */
  --apple-hover: rgba(255, 255, 255, 0.08);
  --apple-active: rgba(255, 255, 255, 0.12);
  --apple-focus: rgba(10, 132, 255, 0.3);
  --apple-disabled: rgba(235, 235, 245, 0.3);
}

/* 全局暗色模式基础样式 */
.dark body {
  background-color: var(--apple-system-background);
  color: var(--apple-label);
}

/* 强制所有文字元素在暗色模式下使用正确的颜色 */
.dark,
.dark * {
  color: var(--apple-label) !important;
}

/* Typography组件强制白色文字 */
.dark .ant-typography,
.dark .ant-typography *,
.dark .ant-typography h1,
.dark .ant-typography h2,
.dark .ant-typography h3,
.dark .ant-typography h4,
.dark .ant-typography h5,
.dark .ant-typography h6,
.dark .ant-typography p,
.dark .ant-typography span,
.dark .ant-typography div {
  color: var(--apple-label) !important;
}

/* 标题组件强制白色文字 */
.dark h1,
.dark h2,
.dark h3,
.dark h4,
.dark h5,
.dark h6 {
  color: var(--apple-label) !important;
}

/* Text组件强制白色文字 */
.dark .ant-typography-caption,
.dark .ant-typography-paragraph,
.dark .ant-typography-title {
  color: var(--apple-label) !important;
}

/* 次要文字保持适当的透明度 */
.dark .ant-typography-secondary {
  color: var(--apple-secondary-label) !important;
}

/* 表单标签强制白色文字 */
.dark .ant-form-item-label > label {
  color: var(--apple-label) !important;
}

/* 卡片标题强制白色文字 */
.dark .ant-card-head-title {
  color: var(--apple-label) !important;
}

/* 最高优先级的文字颜色强制规则 */
.dark .ant-typography,
.dark .ant-typography *,
.dark span:not(.ant-input):not(.ant-select-selection-search-input),
.dark div:not(.ant-input):not(.ant-select-selection-search-input) > span,
.dark .ant-space-item span,
.dark .ant-badge-status-text,
.dark strong,
.dark b {
  color: var(--apple-label) !important;
}

/* 特殊状态文字保持其原有颜色但确保可见性 */
.dark .ant-typography[style*="color"] {
  opacity: 1 !important;
  filter: brightness(1.5) !important;
}

/* 选择器和下拉菜单优化 */
.dark .ant-select-dropdown {
  background: var(--apple-secondary-background) !important;
  border-color: var(--apple-separator) !important;
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.8) !important;
}

.dark .ant-select-item {
  color: var(--apple-label) !important;
}

.dark .ant-select-item:hover {
  background: var(--apple-tertiary-background) !important;
}

.dark .ant-select-item-option-selected {
  background: rgba(10, 132, 255, 0.2) !important;
  color: var(--apple-label) !important;
}

/* 工具提示优化 */
.dark .ant-tooltip-inner {
  background: var(--apple-tertiary-background) !important;
  color: var(--apple-label) !important;
  border: 1px solid var(--apple-separator) !important;
}

.dark .ant-tooltip-arrow::before {
  background: var(--apple-tertiary-background) !important;
  border-color: var(--apple-separator) !important;
}

/* 消息提示优化 */
.dark .ant-message-notice {
  background: var(--apple-secondary-background) !important;
  border: 1px solid var(--apple-separator) !important;
  color: var(--apple-label) !important;
}

/* 标签优化 */
.dark .ant-tag {
  background: var(--apple-tertiary-background) !important;
  border-color: var(--apple-separator) !important;
  color: var(--apple-label) !important;
}

/* 徽章优化 */
.dark .ant-badge-status-text {
  color: var(--apple-secondary-label) !important;
}

/* 分割线优化 */
.dark .ant-divider {
  border-color: var(--apple-separator) !important;
}

/* 抽屉和模态框优化 */
.dark .ant-drawer-content {
  background: var(--apple-secondary-background) !important;
}

.dark .ant-drawer-header {
  background: var(--apple-secondary-background) !important;
  border-bottom-color: var(--apple-separator) !important;
}

.dark .ant-modal-content {
  background: var(--apple-secondary-background) !important;
  border: 1px solid var(--apple-separator) !important;
}

.dark .ant-modal-header {
  background: var(--apple-secondary-background) !important;
  border-bottom-color: var(--apple-separator) !important;
}

.dark .ant-modal-title {
  color: var(--apple-label) !important;
}

/* 步骤条优化 */
.dark .ant-steps-item-title {
  color: var(--apple-label) !important;
}

.dark .ant-steps-item-description {
  color: var(--apple-secondary-label) !important;
}

/* 面包屑优化 */
.dark .ant-breadcrumb a {
  color: var(--apple-blue) !important;
}

.dark .ant-breadcrumb-separator {
  color: var(--apple-secondary-label) !important;
}

/* 菜单优化 */
.dark .ant-menu {
  background: var(--apple-secondary-background) !important;
  border-color: var(--apple-separator) !important;
}

.dark .ant-menu-item {
  color: var(--apple-label) !important;
}

.dark .ant-menu-item:hover {
  background: var(--apple-tertiary-background) !important;
}

.dark .ant-menu-item-selected {
  background: rgba(10, 132, 255, 0.2) !important;
  color: var(--apple-blue) !important;
}

/* 标签页优化 */
.dark .ant-tabs-tab {
  color: var(--apple-secondary-label) !important;
}

.dark .ant-tabs-tab-active {
  color: var(--apple-blue) !important;
}

.dark .ant-tabs-ink-bar {
  background: var(--apple-blue) !important;
}

/* 折叠面板优化 */
.dark .ant-collapse {
  background: var(--apple-secondary-background) !important;
  border-color: var(--apple-separator) !important;
}

.dark .ant-collapse-header {
  color: var(--apple-label) !important;
}

.dark .ant-collapse-content {
  background: var(--apple-secondary-background) !important;
  border-color: var(--apple-separator) !important;
}

/* 时间选择器优化 */
.dark .ant-picker {
  background: var(--apple-secondary-background) !important;
  border-color: var(--apple-separator) !important;
  color: var(--apple-label) !important;
}

.dark .ant-picker:hover {
  border-color: var(--apple-blue) !important;
}

.dark .ant-picker-focused {
  border-color: var(--apple-blue) !important;
  box-shadow: 0 0 0 3px var(--apple-focus) !important;
}

/* 上传组件优化 */
.dark .ant-upload {
  background: var(--apple-secondary-background) !important;
  border-color: var(--apple-separator) !important;
}

.dark .ant-upload-drag {
  background: var(--apple-tertiary-background) !important;
  border-color: var(--apple-separator) !important;
}

.dark .ant-upload-drag:hover {
  border-color: var(--apple-blue) !important;
}

/* 进度条优化 */
.dark .ant-progress-text {
  color: var(--apple-label) !important;
}

/* 评分组件优化 */
.dark .ant-rate-star {
  color: var(--apple-quaternary-label) !important;
}

.dark .ant-rate-star-full {
  color: var(--apple-blue) !important;
}

/* 滑块优化 */
.dark .ant-slider-track {
  background: var(--apple-blue) !important;
}

.dark .ant-slider-handle {
  border-color: var(--apple-blue) !important;
  background: var(--apple-label) !important;
}

/* 开关优化 */
.dark .ant-switch {
  background: var(--apple-quaternary-label) !important;
}

.dark .ant-switch-checked {
  background: var(--apple-blue) !important;
}

/* 树形控件优化 */
.dark .ant-tree {
  background: var(--apple-secondary-background) !important;
}

.dark .ant-tree-node-content-wrapper {
  color: var(--apple-label) !important;
}

.dark .ant-tree-node-content-wrapper:hover {
  background: var(--apple-tertiary-background) !important;
}

.dark .ant-tree-node-selected {
  background: rgba(10, 132, 255, 0.2) !important;
}

/* 穿梭框优化 */
.dark .ant-transfer-list {
  background: var(--apple-secondary-background) !important;
  border-color: var(--apple-separator) !important;
}

.dark .ant-transfer-list-header {
  background: var(--apple-tertiary-background) !important;
  border-bottom-color: var(--apple-separator) !important;
  color: var(--apple-label) !important;
}

/* 锚点优化 */
.dark .ant-anchor-link-title {
  color: var(--apple-label) !important;
}

.dark .ant-anchor-link-active .ant-anchor-link-title {
  color: var(--apple-blue) !important;
}

/* 回到顶部优化 */
.dark .ant-back-top {
  background: var(--apple-secondary-background) !important;
  border: 1px solid var(--apple-separator) !important;
  color: var(--apple-label) !important;
}

/* 空状态优化 */
.dark .ant-empty-description {
  color: var(--apple-secondary-label) !important;
}

/* 结果页优化 */
.dark .ant-result-title {
  color: var(--apple-label) !important;
}

.dark .ant-result-subtitle {
  color: var(--apple-secondary-label) !important;
}

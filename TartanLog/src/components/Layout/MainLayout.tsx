import React, { useState } from 'react';
import { Layout, Button, Typography, Space, Badge, Tooltip } from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  BulbOutlined,
  BulbFilled,
  WifiOutlined,
  DisconnectOutlined,
} from '@ant-design/icons';
import { useTheme } from '../../hooks/useTheme';
import { useAppStore } from '../../store/useAppStore';
import { ConnectionStatus } from '../Common/StatusIndicator';

const { Header, Sider, Content } = Layout;
const { Title, Text } = Typography;

interface MainLayoutProps {
  children: React.ReactNode;
}

export const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false);
  const { theme, toggleTheme } = useTheme();
  const { connection, connect, disconnect } = useAppStore();

  return (
    <Layout className="min-h-screen">
      {/* 侧边栏 */}
      <Sider 
        trigger={null} 
        collapsible 
        collapsed={collapsed}
        className="shadow-lg"
        width={280}
        collapsedWidth={80}
      >
        <div className="p-4">
          {/* Logo区域 */}
          <div className="flex items-center justify-center mb-6">
            {!collapsed ? (
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center shadow-lg">
                  <span className="text-white font-bold text-lg">T</span>
                </div>
                <div>
                  <Title level={5} className="!mb-0 !text-slate-800 dark:!text-white">
                    达坦系统
                  </Title>
                  <Text className="text-xs text-slate-500 dark:text-white">
                    Gamma成像系统
                  </Text>
                </div>
              </div>
            ) : (
              <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center shadow-lg">
                <span className="text-white font-bold text-lg">T</span>
              </div>
            )}
          </div>

          {/* 连接状态指示器 */}
          <div className="mb-4">
            {!collapsed ? (
              <ConnectionStatus
                isConnected={connection.isConnected}
                deviceName={connection.deviceName}
                className="text-sm"
              />
            ) : (
              <div className="flex justify-center">
                <div
                  className={`w-3 h-3 rounded-full ${
                    connection.isConnected ? 'bg-green-500' : 'bg-red-500'
                  }`}
                />
              </div>
            )}
          </div>

          {/* 导航菜单区域 - 后续添加 */}
          {!collapsed && (
            <div className="space-y-2">
              <div className="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-2">
                功能模块
              </div>
              {/* 这里后续会添加导航菜单项 */}
            </div>
          )}
        </div>
      </Sider>

      {/* 主内容区域 */}
      <Layout>
        {/* 顶部导航栏 */}
        <Header className="!px-4 shadow-sm border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between h-full">
            {/* 左侧：折叠按钮和标题 */}
            <div className="flex items-center space-x-4">
              <Button
                type="text"
                icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
                onClick={() => setCollapsed(!collapsed)}
                className="!w-10 !h-10"
              />
              <Title level={4} className="!mb-0 !text-slate-800 dark:!text-white !font-semibold">
                达坦实时Gamma成像系统
              </Title>
            </div>

            {/* 右侧：工具栏 */}
            <Space size="middle">
              {/* 连接状态按钮 */}
              <Tooltip title={connection.isConnected ? "断开连接" : "连接设备"}>
                <Button
                  type={connection.isConnected ? "primary" : "default"}
                  icon={connection.isConnected ? <WifiOutlined /> : <DisconnectOutlined />}
                  onClick={() => connection.isConnected ? disconnect() : connect()}
                  className="!flex !items-center"
                >
                  {connection.isConnected ? "已连接" : "未连接"}
                </Button>
              </Tooltip>

              {/* 主题切换按钮 */}
              <Tooltip title={theme === 'light' ? "切换到暗色模式" : "切换到亮色模式"}>
                <Button
                  type="text"
                  icon={theme === 'light' ? <BulbOutlined /> : <BulbFilled />}
                  onClick={toggleTheme}
                  className="!w-10 !h-10"
                />
              </Tooltip>
            </Space>
          </div>
        </Header>

        {/* 内容区域 */}
        <Content className="p-6 overflow-auto">
          {children}
        </Content>
      </Layout>
    </Layout>
  );
};

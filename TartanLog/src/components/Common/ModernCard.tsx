import React from 'react';
import { Card, CardProps } from 'antd';
import { useTheme } from '../../hooks/useTheme';

interface ModernCardProps extends CardProps {
  gradient?: boolean;
  glassEffect?: boolean;
  children: React.ReactNode;
}

export const ModernCard: React.FC<ModernCardProps> = ({
  gradient = false,
  glassEffect = false,
  className = '',
  children,
  ...props
}) => {
  const { theme } = useTheme();

  const getCardStyles = () => {
    let styles = 'transition-all duration-300 hover:shadow-xl';
    
    if (gradient) {
      if (theme === 'light') {
        styles += ' bg-gradient-to-br from-white to-slate-50';
      } else {
        // 苹果风格渐变
        styles += ' bg-gradient-to-br from-[#1C1C1E] to-[#2C2C2E]';
      }
    }

    if (glassEffect) {
      if (theme === 'light') {
        styles += ' backdrop-blur-sm bg-white/80 border border-slate-200/50';
      } else {
        // 苹果风格玻璃效果
        styles += ' backdrop-blur-sm bg-[#1C1C1E]/80 border border-[#38383A]/50';
      }
    }
    
    return styles;
  };

  return (
    <Card
      {...props}
      className={`${getCardStyles()} ${className}`}
      style={{
        borderRadius: '16px',
        border: theme === 'light' ? '1px solid #e2e8f0' : '1px solid #38383A',
        ...props.style,
      }}
    >
      {children}
    </Card>
  );
};

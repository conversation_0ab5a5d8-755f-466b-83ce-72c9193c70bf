import React from 'react';
import { Badge, Tooltip, Typography } from 'antd';
import { 
  CheckCircleOutlined, 
  ExclamationCircleOutlined, 
  CloseCircleOutlined,
  LoadingOutlined,
  WifiOutlined,
  DisconnectOutlined
} from '@ant-design/icons';

const { Text } = Typography;

export type StatusType = 'success' | 'warning' | 'error' | 'loading' | 'default';

interface StatusIndicatorProps {
  status: StatusType;
  text?: string;
  tooltip?: string;
  showIcon?: boolean;
  size?: 'small' | 'default' | 'large';
  className?: string;
}

const statusConfig = {
  success: {
    color: '#52c41a',
    icon: CheckCircleOutlined,
    badgeStatus: 'success' as const,
  },
  warning: {
    color: '#faad14',
    icon: ExclamationCircleOutlined,
    badgeStatus: 'warning' as const,
  },
  error: {
    color: '#ff4d4f',
    icon: CloseCircleOutlined,
    badgeStatus: 'error' as const,
  },
  loading: {
    color: '#1890ff',
    icon: LoadingOutlined,
    badgeStatus: 'processing' as const,
  },
  default: {
    color: '#d9d9d9',
    icon: DisconnectOutlined,
    badgeStatus: 'default' as const,
  },
};

export const StatusIndicator: React.FC<StatusIndicatorProps> = ({
  status,
  text,
  tooltip,
  showIcon = true,
  size = 'default',
  className = '',
}) => {
  const config = statusConfig[status];
  const IconComponent = config.icon;

  const sizeStyles = {
    small: 'text-xs',
    default: 'text-sm',
    large: 'text-base',
  };

  const indicator = (
    <div className={`flex items-center space-x-2 ${sizeStyles[size]} ${className}`}>
      {showIcon && (
        <IconComponent 
          style={{ color: config.color }} 
          spin={status === 'loading'}
        />
      )}
      {text && (
        <Text className={`${sizeStyles[size]} text-slate-700 dark:text-white`} style={{ color: config.color }}>
          {text}
        </Text>
      )}
      {!text && !showIcon && (
        <Badge status={config.badgeStatus} />
      )}
    </div>
  );

  if (tooltip) {
    return (
      <Tooltip title={tooltip}>
        {indicator}
      </Tooltip>
    );
  }

  return indicator;
};

// 连接状态专用组件
interface ConnectionStatusProps {
  isConnected: boolean;
  isConnecting?: boolean;
  deviceName?: string;
  className?: string;
}

export const ConnectionStatus: React.FC<ConnectionStatusProps> = ({
  isConnected,
  isConnecting = false,
  deviceName,
  className,
}) => {
  if (isConnecting) {
    return (
      <StatusIndicator
        status="loading"
        text="连接中..."
        tooltip="正在连接设备"
        className={className}
      />
    );
  }

  if (isConnected) {
    return (
      <StatusIndicator
        status="success"
        text={deviceName ? `已连接 - ${deviceName}` : "设备已连接"}
        tooltip={`设备连接正常${deviceName ? ` (${deviceName})` : ''}`}
        className={className}
      />
    );
  }

  return (
    <StatusIndicator
      status="error"
      text="设备未连接"
      tooltip="设备未连接，请检查连接状态"
      className={className}
    />
  );
};

// 数据状态专用组件
interface DataStatusProps {
  isReceiving: boolean;
  dataCount?: number;
  lastUpdateTime?: Date;
  className?: string;
}

export const DataStatus: React.FC<DataStatusProps> = ({
  isReceiving,
  dataCount = 0,
  lastUpdateTime,
  className,
}) => {
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('zh-CN', { 
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  if (isReceiving) {
    return (
      <StatusIndicator
        status="success"
        text={`接收中 (${dataCount})`}
        tooltip={`正在接收数据，已接收 ${dataCount} 条${lastUpdateTime ? `，最后更新: ${formatTime(lastUpdateTime)}` : ''}`}
        className={className}
      />
    );
  }

  if (dataCount > 0) {
    return (
      <StatusIndicator
        status="warning"
        text={`已停止 (${dataCount})`}
        tooltip={`数据接收已停止，共接收 ${dataCount} 条数据${lastUpdateTime ? `，最后更新: ${formatTime(lastUpdateTime)}` : ''}`}
        className={className}
      />
    );
  }

  return (
    <StatusIndicator
      status="default"
      text="等待数据"
      tooltip="等待数据接收"
      className={className}
    />
  );
};

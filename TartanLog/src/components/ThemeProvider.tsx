import React from 'react';
import { ConfigProvider, App as AntApp } from 'antd';
import { ThemeContext, useThemeState } from '../hooks/useTheme';
import { themeConfig } from '../config/theme';
import zhCN from 'antd/locale/zh_CN';

interface ThemeProviderProps {
  children: React.ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const themeState = useThemeState();

  return (
    <ThemeContext.Provider value={themeState}>
      <ConfigProvider
        theme={themeConfig[themeState.theme]}
        locale={zhCN}
      >
        <AntApp>
          {children}
        </AntApp>
      </ConfigProvider>
    </ThemeContext.Provider>
  );
};

import React, { useState, useEffect } from 'react';
import {
  Button,
  Input,
  Select,
  Checkbox,
  Table,
  Badge,
  Space,
  Typography,
  Row,
  Col,
  InputNumber,
} from 'antd';
import {
  PlayCircleOutlined,
  PauseOutlined,
  UploadOutlined,
  DeleteOutlined,
  SettingOutlined,
  LineChartOutlined,
  DatabaseOutlined,
  ExportOutlined,
} from '@ant-design/icons';
import { ModernCard } from './Common/ModernCard';

const { Title, Text } = Typography;
const { Option } = Select;

interface GammaData {
  key: string;
  path: string;
  depth: number;
  label: string;
  value: number;
  time: string;
  scale: number;
  offset: number;
  length: number;
  result: string;
}

interface ImagingSettings {
  angle: number;
  left: number;
  right: number;
  up: number;
  down: number;
}

export const GammaImagingSystem: React.FC = () => {
  const [isConnected, setIsConnected] = useState(false);
  const [isRunning, setIsRunning] = useState(false);
  const [jobId, setJobId] = useState('');
  const [wellNumber, setWellNumber] = useState('');
  const [refreshInterval, setRefreshInterval] = useState(60);
  const [smoothCurve, setSmoothCurve] = useState(false);
  const [gammaData, setGammaData] = useState<GammaData[]>([]);
  const [imagingSettings, setImagingSettings] = useState<ImagingSettings>({
    angle: 0,
    left: 0,
    right: 0,
    up: 0,
    down: 0,
  });

  // 模拟实时数据更新
  useEffect(() => {
    if (isRunning) {
      const interval = setInterval(() => {
        const newData: GammaData = {
          key: `${Date.now()}`,
          path: `路径${Math.floor(Math.random() * 100)}`,
          depth: Math.random() * 1000,
          label: `标签${Math.floor(Math.random() * 10)}`,
          value: Math.random() * 100,
          time: new Date().toLocaleTimeString(),
          scale: Math.random() * 10,
          offset: Math.random() * 5,
          length: Math.random() * 50,
          result: Math.random() > 0.5 ? '正常' : '异常',
        };
        setGammaData((prev) => [newData, ...prev.slice(0, 9)]);
      }, refreshInterval * 1000);

      return () => clearInterval(interval);
    }
  }, [isRunning, refreshInterval]);

  const handleConnect = () => {
    setIsConnected(!isConnected);
  };

  const handleStartStop = () => {
    setIsRunning(!isRunning);
  };

  const handleExport = () => {
    console.log('导出数据');
  };

  const handleClearData = () => {
    setGammaData([]);
  };

  const handleResetSettings = () => {
    setImagingSettings({ angle: 0, left: 0, right: 0, up: 0, down: 0 });
  };

  const handleStandardSettings = () => {
    setImagingSettings({ angle: 90, left: -10, right: 10, up: 10, down: -10 });
  };

  const tableColumns = [
    {
      title: '路径',
      dataIndex: 'path',
      key: 'path',
      width: 100,
    },
    {
      title: '源深',
      dataIndex: 'depth',
      key: 'depth',
      width: 80,
      render: (value: number) => value.toFixed(2),
    },
    {
      title: '标签',
      dataIndex: 'label',
      key: 'label',
      width: 80,
    },
    {
      title: '数值',
      dataIndex: 'value',
      key: 'value',
      width: 80,
      render: (value: number) => value.toFixed(2),
    },
    {
      title: '时间',
      dataIndex: 'time',
      key: 'time',
      width: 100,
    },
    {
      title: 'Scale',
      dataIndex: 'scale',
      key: 'scale',
      width: 80,
      render: (value: number) => value.toFixed(2),
    },
    {
      title: 'Offset',
      dataIndex: 'offset',
      key: 'offset',
      width: 80,
      render: (value: number) => value.toFixed(2),
    },
    {
      title: '套长',
      dataIndex: 'length',
      key: 'length',
      width: 80,
      render: (value: number) => value.toFixed(2),
    },
    {
      title: '结果',
      dataIndex: 'result',
      key: 'result',
      width: 80,
      render: (result: string) => (
        <Badge 
          status={result === '正常' ? 'success' : 'error'} 
          text={result}
        />
      ),
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-[#000000] dark:to-[#1C1C1E] p-4">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* 头部 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="text-3xl font-bold text-blue-600 dark:text-blue-400">Tartan</div>
            <div className="text-xl font-semibold text-gray-800 dark:text-white">达坦实时Gamma成像系统</div>
            <Badge 
              status={isConnected ? 'success' : 'error'} 
              text={isConnected ? '已连接' : '未连接'}
              className="ml-4"
            />
          </div>
          <Space>
            <Button icon={<SettingOutlined />} size="small">
              设置
            </Button>
            <Button icon={<ExportOutlined />} size="small">
              导出
            </Button>
          </Space>
        </div>

        <Row gutter={24}>
          {/* 左侧控制面板 */}
          <Col xs={24} lg={8}>
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
              {/* Job设置 */}
              <ModernCard
                title={
                  <div className="flex items-center">
                    <DatabaseOutlined className="mr-2 text-blue-600 dark:text-blue-400" />
                    <span className="text-slate-800 dark:text-white">Job设置</span>
                  </div>
                }
                gradient={true}
              >
                <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                  <div>
                    <Text className="text-slate-700 dark:text-white">JobId</Text>
                    <div className="flex space-x-2 mt-1">
                      <Input
                        value={jobId}
                        onChange={(e) => setJobId(e.target.value)}
                        placeholder="输入Job ID"
                        className="flex-1"
                      />
                      <Button icon={<UploadOutlined />} size="small" />
                    </div>
                  </div>

                  <div>
                    <Text className="text-slate-700 dark:text-white">井号</Text>
                    <div className="flex space-x-2 mt-1">
                      <Input
                        value={wellNumber}
                        onChange={(e) => setWellNumber(e.target.value)}
                        placeholder="输入井号"
                        className="flex-1"
                      />
                      <Select defaultValue="趋钻" style={{ width: 80 }}>
                        <Option value="趋钻">趋钻</Option>
                        <Option value="其他">其他</Option>
                      </Select>
                    </div>
                  </div>

                  <div className="flex space-x-2 pt-2">
                    <Button 
                      onClick={handleConnect} 
                      type={isConnected ? 'default' : 'primary'}
                      danger={isConnected}
                      className="flex-1"
                    >
                      {isConnected ? '断开' : '连接'}
                    </Button>
                    <Button className="flex-1">数据传输</Button>
                    <Button className="flex-1">接收</Button>
                  </div>
                </Space>
              </ModernCard>

              {/* Gamma设置 */}
              <ModernCard
                title={
                  <div className="flex items-center">
                    <SettingOutlined className="mr-2 text-blue-600 dark:text-blue-400" />
                    <span className="text-slate-800 dark:text-white">Gamma设置</span>
                  </div>
                }
                gradient={true}
              >
                <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                  <div className="flex items-center space-x-2">
                    <Select placeholder="选择配置" className="flex-1">
                      <Option value="config1">配置1</Option>
                      <Option value="config2">配置2</Option>
                    </Select>
                    <Button size="small">添加</Button>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      checked={smoothCurve}
                      onChange={(e) => setSmoothCurve(e.target.checked)}
                    >
                      <Text className="text-slate-700 dark:text-white">曲线平滑</Text>
                    </Checkbox>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Text className="text-slate-700 dark:text-white text-sm">刷新间隔(秒)</Text>
                    <InputNumber
                      value={refreshInterval}
                      onChange={(value) => setRefreshInterval(value || 60)}
                      style={{ width: 80 }}
                      min={1}
                    />
                    <Button size="small">确定</Button>
                  </div>

                  <Button 
                    onClick={handleClearData} 
                    icon={<DeleteOutlined />} 
                    block
                  >
                    清空
                  </Button>
                </Space>
              </ModernCard>

              {/* 成像设置 */}
              <ModernCard
                title={<span className="text-slate-800 dark:text-white">成像设置</span>}
                gradient={true}
              >
                <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                  {/* 扇区设置 */}
                  <div className="flex items-center space-x-3">
                    <Text className="w-12 text-sm font-medium text-slate-700 dark:text-white">扇区</Text>
                    <InputNumber
                      value={imagingSettings.angle}
                      onChange={(value) =>
                        setImagingSettings((prev) => ({
                          ...prev,
                          angle: value || 0,
                        }))
                      }
                      style={{ width: 80 }}
                      placeholder="0"
                    />
                    <Text className="text-xs text-gray-500 dark:text-gray-400">度</Text>
                  </div>

                  {/* 方向控制 */}
                  <div>
                    <Text className="text-sm font-medium text-gray-700 dark:text-white">方向调整</Text>
                    <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg mt-2">
                      <div className="grid grid-cols-3 gap-3 items-center">
                        {/* 第一行：上 */}
                        <div></div>
                        <div className="flex flex-col items-center">
                          <Text className="text-xs text-gray-600 dark:text-gray-400 mb-1">上</Text>
                          <InputNumber
                            value={imagingSettings.up}
                            onChange={(value) =>
                              setImagingSettings((prev) => ({
                                ...prev,
                                up: value || 0,
                              }))
                            }
                            style={{ width: 64 }}
                            size="small"
                            placeholder="0"
                          />
                        </div>
                        <div></div>

                        {/* 第二行：左、中心、右 */}
                        <div className="flex flex-col items-center">
                          <Text className="text-xs text-gray-600 dark:text-gray-400 mb-1">左</Text>
                          <InputNumber
                            value={imagingSettings.left}
                            onChange={(value) =>
                              setImagingSettings((prev) => ({
                                ...prev,
                                left: value || 0,
                              }))
                            }
                            style={{ width: 64 }}
                            size="small"
                            placeholder="0"
                          />
                        </div>
                        <div className="flex items-center justify-center">
                          <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 border-2 border-blue-300 dark:border-blue-600 rounded-full flex items-center justify-center">
                            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          </div>
                        </div>
                        <div className="flex flex-col items-center">
                          <Text className="text-xs text-gray-600 dark:text-gray-400 mb-1">右</Text>
                          <InputNumber
                            value={imagingSettings.right}
                            onChange={(value) =>
                              setImagingSettings((prev) => ({
                                ...prev,
                                right: value || 0,
                              }))
                            }
                            style={{ width: 64 }}
                            size="small"
                            placeholder="0"
                          />
                        </div>

                        {/* 第三行：下 */}
                        <div></div>
                        <div className="flex flex-col items-center">
                          <Text className="text-xs text-gray-600 dark:text-gray-400 mb-1">下</Text>
                          <InputNumber
                            value={imagingSettings.down}
                            onChange={(value) =>
                              setImagingSettings((prev) => ({
                                ...prev,
                                down: value || 0,
                              }))
                            }
                            style={{ width: 64 }}
                            size="small"
                            placeholder="0"
                          />
                        </div>
                        <div></div>
                      </div>
                    </div>
                  </div>

                  {/* 快速设置按钮 */}
                  <div>
                    <Text className="text-sm font-medium text-gray-700 dark:text-white">快速设置</Text>
                    <div className="flex space-x-2 mt-2">
                      <Button size="small" onClick={handleResetSettings}>
                        重置
                      </Button>
                      <Button size="small" onClick={handleStandardSettings}>
                        标准
                      </Button>
                    </div>
                  </div>

                  <Button type="primary" block className="bg-blue-600 hover:bg-blue-700">
                    应用设置
                  </Button>
                </Space>
              </ModernCard>
            </Space>
          </Col>

          {/* 右侧显示区域 */}
          <Col xs={24} lg={16}>
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
              {/* 实时Gamma曲线 */}
              <ModernCard
                title={
                  <div className="flex items-center justify-between w-full">
                    <div className="flex items-center">
                      <LineChartOutlined className="mr-2 text-blue-600 dark:text-blue-400" />
                      <span className="text-slate-800 dark:text-white">实时Gamma曲线</span>
                    </div>
                    <Space>
                      <Button
                        onClick={handleStartStop}
                        size="small"
                        type={isRunning ? 'default' : 'primary'}
                        danger={isRunning}
                        icon={isRunning ? <PauseOutlined /> : <PlayCircleOutlined />}
                      >
                        {isRunning ? '停止' : '开始'}
                      </Button>
                      <Button onClick={handleExport} size="small" icon={<ExportOutlined />}>
                        导出
                      </Button>
                    </Space>
                  </div>
                }
                gradient={true}
              >
                <div className="h-96 bg-gray-50 dark:bg-gray-800 border-2 border-dashed border-gray-200 dark:border-gray-600 rounded-lg flex items-center justify-center">
                  <div className="text-center text-gray-500 dark:text-gray-400">
                    <LineChartOutlined className="text-5xl mb-4 opacity-50" />
                    <Title level={4} className="text-gray-600 dark:text-gray-300">实时Gamma曲线显示区域</Title>
                    <Text className="text-sm">
                      {isRunning ? '正在采集数据...' : '点击开始按钮开始数据采集'}
                    </Text>
                  </div>
                </div>
              </ModernCard>

              {/* 数据表格 */}
              <ModernCard
                title={<span className="text-slate-800 dark:text-white">数据记录</span>}
                gradient={true}
              >
                <Table
                  columns={tableColumns}
                  dataSource={gammaData}
                  pagination={false}
                  scroll={{ y: 400 }}
                  size="small"
                  locale={{
                    emptyText: (
                      <div className="text-center text-gray-500 dark:text-gray-400 py-8">
                        <DatabaseOutlined className="text-3xl mb-2 opacity-50" />
                        <div>暂无数据</div>
                      </div>
                    ),
                  }}
                />
              </ModernCard>
            </Space>
          </Col>
        </Row>
      </div>
    </div>
  );
};

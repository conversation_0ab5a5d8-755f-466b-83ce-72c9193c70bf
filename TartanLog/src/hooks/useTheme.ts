import { useState, useEffect, createContext, useContext } from 'react';
import { ThemeMode } from '../config/theme';

interface ThemeContextType {
  theme: ThemeMode;
  toggleTheme: () => void;
  setTheme: (theme: ThemeMode) => void;
}

export const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export const useThemeState = () => {
  // 从localStorage读取保存的主题，默认为light
  const [theme, setThemeState] = useState<ThemeMode>(() => {
    if (typeof window !== 'undefined') {
      const savedTheme = localStorage.getItem('tartan-theme') as ThemeMode;
      return savedTheme || 'light';
    }
    return 'light';
  });

  // 切换主题
  const toggleTheme = () => {
    setThemeState(prevTheme => prevTheme === 'light' ? 'dark' : 'light');
  };

  // 设置特定主题
  const setTheme = (newTheme: ThemeMode) => {
    setThemeState(newTheme);
  };

  // 保存主题到localStorage并更新HTML class
  useEffect(() => {
    localStorage.setItem('tartan-theme', theme);
    
    // 更新HTML根元素的class，用于Tailwind的暗色模式
    const root = document.documentElement;
    if (theme === 'dark') {
      root.classList.add('dark');
    } else {
      root.classList.remove('dark');
    }
  }, [theme]);

  return {
    theme,
    toggleTheme,
    setTheme,
  };
};

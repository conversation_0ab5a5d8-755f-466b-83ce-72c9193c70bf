import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

// 连接状态类型
export interface ConnectionState {
  isConnected: boolean;
  deviceName?: string;
  connectionTime?: Date;
  lastError?: string;
}

// Job设置状态
export interface JobSettings {
  jobId: string;
  wellName: string;
  run: string;
}

// Gamma设置状态
export interface GammaSettings {
  selectedGRs: string[];
  smoothEnabled: boolean;
  refreshInterval: number;
}

// 成像设置状态
export interface ImagingSettings {
  sector: string;
  directions: {
    left: string;
    right: string;
    up: string;
    down: string;
  };
}

// 应用状态接口
interface AppState {
  // 连接状态
  connection: ConnectionState;
  
  // 设置状态
  jobSettings: JobSettings;
  gammaSettings: GammaSettings;
  imagingSettings: ImagingSettings;
  
  // 操作方法
  setConnection: (connection: Partial<ConnectionState>) => void;
  setJobSettings: (settings: Partial<JobSettings>) => void;
  setGammaSettings: (settings: Partial<GammaSettings>) => void;
  setImagingSettings: (settings: Partial<ImagingSettings>) => void;
  
  // 业务操作
  connect: () => Promise<void>;
  disconnect: () => Promise<void>;
  exportData: () => Promise<void>;
  startDataTransfer: () => Promise<void>;
  startNetListen: () => Promise<void>;
}

// 创建状态管理store
export const useAppStore = create<AppState>()(
  devtools(
    (set, get) => ({
      // 初始状态
      connection: {
        isConnected: false,
      },
      
      jobSettings: {
        jobId: '',
        wellName: '',
        run: '',
      },

      gammaSettings: {
        selectedGRs: [],
        smoothEnabled: false,
        refreshInterval: 5,
      },
      
      imagingSettings: {
        sector: '',
        directions: {
          left: '',
          right: '',
          up: '',
          down: '',
        },
      },
      
      // 状态更新方法
      setConnection: (connection) =>
        set((state) => ({
          connection: { ...state.connection, ...connection },
        })),
      
      setJobSettings: (settings) =>
        set((state) => ({
          jobSettings: { ...state.jobSettings, ...settings },
        })),
      
      setGammaSettings: (settings) =>
        set((state) => ({
          gammaSettings: { ...state.gammaSettings, ...settings },
        })),
      
      setImagingSettings: (settings) =>
        set((state) => ({
          imagingSettings: { ...state.imagingSettings, ...settings },
        })),
      
      // 业务操作方法
      connect: async () => {
        const { connection } = get();
        if (connection.isConnected) {
          return;
        }
        
        try {
          // 这里后续会调用Tauri命令连接设备
          console.log('正在连接设备...');
          
          // 模拟连接过程
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          set((state) => ({
            connection: {
              ...state.connection,
              isConnected: true,
              deviceName: '达坦Gamma设备',
              connectionTime: new Date(),
              lastError: undefined,
            },
          }));
          
          console.log('设备连接成功');
        } catch (error) {
          console.error('连接失败:', error);
          set((state) => ({
            connection: {
              ...state.connection,
              isConnected: false,
              lastError: error instanceof Error ? error.message : '连接失败',
            },
          }));
          throw error;
        }
      },
      
      disconnect: async () => {
        const { connection } = get();
        if (!connection.isConnected) {
          return;
        }
        
        try {
          // 这里后续会调用Tauri命令断开设备
          console.log('正在断开设备...');
          
          // 模拟断开过程
          await new Promise(resolve => setTimeout(resolve, 500));
          
          set((state) => ({
            connection: {
              ...state.connection,
              isConnected: false,
              deviceName: undefined,
              connectionTime: undefined,
              lastError: undefined,
            },
          }));
          
          console.log('设备已断开');
        } catch (error) {
          console.error('断开失败:', error);
          throw error;
        }
      },
      
      exportData: async () => {
        console.log('导出数据...');
        // 这里后续会实现数据导出逻辑
      },
      
      startDataTransfer: async () => {
        console.log('开始数据远传...');
        // 这里后续会实现数据远传逻辑
      },
      
      startNetListen: async () => {
        console.log('开始网络接收...');
        // 这里后续会实现网络接收逻辑
      },
    }),
    {
      name: 'tartan-app-store',
    }
  )
);

import { ThemeConfig } from 'antd';

// 2024现代化工业设计主题配置 - 基于最新设计趋势
export const lightTheme: ThemeConfig = {
  token: {
    // 主色调 - 现代化的深蓝色系 (更专业、更现代)
    colorPrimary: '#2563eb', // 现代蓝色，比传统蓝色更深更稳重
    colorSuccess: '#10b981', // 现代绿色，更清新
    colorWarning: '#f59e0b', // 现代橙色，更温和
    colorError: '#ef4444',   // 现代红色，更柔和
    colorInfo: '#06b6d4',    // 现代青色，更清爽

    // 背景色 - 现代中性色系 (更舒适的视觉体验)
    colorBgContainer: '#ffffff',     // 纯白背景，更干净
    colorBgElevated: '#ffffff',      // 卡片背景
    colorBgLayout: '#f8fafc',        // 页面背景，极浅的蓝灰色

    // 边框和分割线 - 更柔和的边界
    colorBorder: '#e2e8f0',          // 柔和的灰蓝色边框
    colorSplit: '#f1f5f9',           // 更浅的分割线

    // 文字颜色 - 现代化的文字层次
    colorText: '#0f172a',            // 深色文字，更好的对比度
    colorTextSecondary: '#475569',   // 中等灰色，更现代
    colorTextTertiary: '#94a3b8',    // 浅灰色，更柔和

    // 圆角 - 现代化设计
    borderRadius: 12,                // 更大的圆角，更现代
    borderRadiusLG: 16,
    borderRadiusSM: 8,

    // 字体 - 现代化字体栈
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", "Noto Sans", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji"',
    fontSize: 14,
    fontSizeLG: 16,
    fontSizeSM: 12,

    // 阴影 - 更现代的阴影效果
    boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1)',
    boxShadowSecondary: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1)',
  },
  components: {
    Layout: {
      headerBg: '#ffffff',
      headerHeight: 64,
      siderBg: '#f8fafc',              // 更现代的侧边栏背景
      bodyBg: '#f8fafc',               // 统一的页面背景
    },
    Card: {
      headerBg: 'transparent',
      actionsBg: '#f8fafc',            // 更柔和的操作区背景
      borderRadiusLG: 16,              // 更大的卡片圆角
    },
    Button: {
      borderRadius: 8,                 // 更现代的按钮圆角
      controlHeight: 36,               // 稍微高一点的按钮
      controlHeightLG: 44,
      controlHeightSM: 28,
      fontWeight: 500,                 // 稍微粗一点的字体
    },
    Input: {
      borderRadius: 8,                 // 更现代的输入框圆角
      controlHeight: 36,               // 统一的高度
    },
    Select: {
      borderRadius: 8,                 // 更现代的选择框圆角
      controlHeight: 36,               // 统一的高度
    },
    Table: {
      borderRadiusLG: 12,              // 表格圆角
    },
  },
};

export const darkTheme: ThemeConfig = {
  token: {
    // 主色调 - 遵循Apple暗色模式设计原则，提供更好的可访问性和视觉舒适度
    colorPrimary: '#0A84FF',          // Apple系统蓝色 (更亮的版本，提高暗色背景下的可见性)
    colorSuccess: '#32D74B',          // Apple系统绿色 (稍微调亮，保持清晰度)
    colorWarning: '#FF9F0A',          // Apple系统橙色 (保持原有的温和警告色)
    colorError: '#FF453A',            // Apple系统红色 (保持清晰的错误提示)
    colorInfo: '#64D2FF',             // Apple系统浅蓝色 (信息色，保持高可见性)

    // 背景色 - 采用Apple推荐的语义化背景层次，提供更好的深度感知
    colorBgContainer: '#1C1C1E',      // Secondary System Background - 容器背景
    colorBgElevated: '#2C2C2E',       // Tertiary System Background - 提升的元素背景
    colorBgLayout: '#000000',         // System Background - 主背景，纯黑提供最佳对比度

    // 边框和分割线 - 使用Apple的分隔符颜色系统，确保适当的视觉分离
    colorBorder: '#38383A',           // Separator - 标准分隔符颜色
    colorSplit: '#48484A',            // Opaque Separator - 不透明分隔符，更明显的分割

    // 文字颜色 - 遵循Apple的标签颜色层次，确保可访问性和可读性
    colorText: '#FFFFFF',             // Label - 主要文字，纯白确保最高对比度
    colorTextSecondary: '#EBEBF599',  // Secondary Label - 次要文字 (60%透明度)
    colorTextTertiary: '#EBEBF54D',   // Tertiary Label - 三级文字 (30%透明度)

    // 圆角 - 与亮色主题保持一致
    borderRadius: 12,
    borderRadiusLG: 16,
    borderRadiusSM: 8,

    // 字体 - 现代化字体栈
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", "Noto Sans", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji"',
    fontSize: 14,
    fontSizeLG: 16,
    fontSizeSM: 12,

    // 阴影 - 针对暗色模式优化的阴影效果，使用更深的阴影增强层次感
    boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.8), 0 1px 2px -1px rgba(0, 0, 0, 0.6)',
    boxShadowSecondary: '0 4px 6px -1px rgba(0, 0, 0, 0.8), 0 2px 4px -2px rgba(0, 0, 0, 0.6)',
  },
  components: {
    Layout: {
      headerBg: '#1C1C1E',             // Secondary System Background - 头部背景
      headerHeight: 64,
      siderBg: '#000000',              // System Background - 侧边栏使用主背景
      bodyBg: '#000000',               // System Background - 主体背景
    },
    Card: {
      headerBg: 'transparent',         // 透明头部，保持一致性
      actionsBg: '#2C2C2E',            // Tertiary System Background - 操作区背景
      borderRadiusLG: 16,              // 现代化圆角设计
      boxShadow: '0 2px 8px 0 rgba(0, 0, 0, 0.6)', // 增强卡片阴影效果
    },
    Typography: {
      titleMarginBottom: 0,
      titleMarginTop: 0,
      colorText: '#FFFFFF',            // Label - 主要标题文字
      colorTextHeading: '#FFFFFF',     // Label - 标题文字，确保高对比度
      colorTextDescription: '#EBEBF599', // Secondary Label - 描述文字
    },
    Button: {
      borderRadius: 8,                 // 现代化圆角设计
      controlHeight: 36,               // 统一的控件高度
      controlHeightLG: 44,
      controlHeightSM: 28,
      fontWeight: 500,                 // 中等字重，提高可读性
      colorText: '#FFFFFF',            // Label - 按钮文字使用主标签色
      colorBgContainer: '#1C1C1E',     // 默认按钮背景使用次要背景色
      colorBorder: '#38383A',          // 按钮边框使用分隔符颜色
    },
    Input: {
      borderRadius: 8,                 // 现代化圆角设计
      controlHeight: 36,               // 统一的控件高度
      colorText: '#FFFFFF',            // Label - 输入文字使用主标签色
      colorTextPlaceholder: '#8E8E93', // Quaternary Label - 占位符使用四级标签色
      colorBgContainer: '#1C1C1E',     // 输入框背景使用次要背景色
      colorBorder: '#38383A',          // 输入框边框使用分隔符颜色
      activeBorderColor: '#0A84FF',    // 激活状态使用主色调
    },
    Select: {
      borderRadius: 8,                 // 现代化圆角设计
      controlHeight: 36,               // 统一的控件高度
      colorText: '#FFFFFF',            // Label - 选择器文字使用主标签色
      colorTextPlaceholder: '#8E8E93', // Quaternary Label - 占位符使用四级标签色
      colorBgContainer: '#1C1C1E',     // 选择器背景使用次要背景色
      colorBorder: '#38383A',          // 选择器边框使用分隔符颜色
      optionSelectedBg: '#2C2C2E',     // 选中项背景使用三级背景色
    },
    Table: {
      borderRadiusLG: 12,              // 表格圆角
      colorText: '#FFFFFF',            // Label - 表格文字使用主标签色
      colorTextHeading: '#FFFFFF',     // Label - 表格标题使用主标签色
      headerBg: '#1C1C1E',             // 表头背景使用次要背景色
      rowHoverBg: '#2C2C2E',           // 行悬停背景使用三级背景色
    },
    Form: {
      labelColor: '#FFFFFF',           // Label - 表单标签使用主标签色
      itemMarginBottom: 16,            // 表单项间距
    },
  },
};

// 主题切换的类型定义
export type ThemeMode = 'light' | 'dark';

// 现代化的颜色变量 - 用于自定义样式
export const modernColors = {
  light: {
    // 主色调渐变
    primaryGradient: 'linear-gradient(135deg, #2563eb 0%, #3b82f6 100%)',
    successGradient: 'linear-gradient(135deg, #10b981 0%, #22c55e 100%)',
    warningGradient: 'linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%)',

    // 背景渐变
    backgroundGradient: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)',
    cardGradient: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',

    // 强调色
    accent: '#8b5cf6',               // 紫色强调
    accentLight: '#a78bfa',          // 浅紫色

    // 中性色
    neutral50: '#f8fafc',
    neutral100: '#f1f5f9',
    neutral200: '#e2e8f0',
    neutral300: '#cbd5e1',
    neutral400: '#94a3b8',
    neutral500: '#64748b',
    neutral600: '#475569',
    neutral700: '#334155',
    neutral800: '#1e293b',
    neutral900: '#0f172a',
  },
  dark: {
    // 主色调渐变 - 基于Apple暗色模式优化的渐变效果
    primaryGradient: 'linear-gradient(135deg, #0A84FF 0%, #64D2FF 100%)',
    successGradient: 'linear-gradient(135deg, #32D74B 0%, #30D158 100%)',
    warningGradient: 'linear-gradient(135deg, #FF9F0A 0%, #FFCC02 100%)',
    errorGradient: 'linear-gradient(135deg, #FF453A 0%, #FF6961 100%)',

    // 背景渐变 - 遵循Apple的背景层次系统
    backgroundGradient: 'linear-gradient(135deg, #000000 0%, #1C1C1E 100%)',
    cardGradient: 'linear-gradient(135deg, #1C1C1E 0%, #2C2C2E 100%)',
    elevatedGradient: 'linear-gradient(135deg, #2C2C2E 0%, #38383A 100%)',

    // 强调色 - Apple系统强调色
    accent: '#BF5AF2',               // System Purple - 系统紫色
    accentLight: '#DA8FFF',          // Light Purple - 浅紫色变体

    // 语义化中性色系 - 完全遵循Apple的暗色模式颜色系统
    neutral50: '#000000',            // System Background - 主背景
    neutral100: '#1C1C1E',           // Secondary System Background - 次要背景
    neutral200: '#2C2C2E',           // Tertiary System Background - 三级背景
    neutral300: '#38383A',           // Separator - 分隔符
    neutral400: '#48484A',           // Opaque Separator - 不透明分隔符
    neutral500: '#636366',           // Placeholder Text - 占位符文字
    neutral600: '#8E8E93',           // Quaternary Label - 四级标签
    neutral700: '#AEAEB2',           // Tertiary Label - 三级标签
    neutral800: '#C7C7CC',           // Secondary Label - 次要标签
    neutral900: '#FFFFFF',           // Label - 主标签

    // 交互状态颜色 - 针对暗色模式优化的交互反馈
    hover: 'rgba(255, 255, 255, 0.08)',      // 悬停状态
    active: 'rgba(255, 255, 255, 0.12)',     // 激活状态
    focus: 'rgba(10, 132, 255, 0.3)',        // 焦点状态
    disabled: 'rgba(235, 235, 245, 0.3)',    // 禁用状态
  }
};

// 主题配置映射
export const themeConfig = {
  light: lightTheme,
  dark: darkTheme,
};

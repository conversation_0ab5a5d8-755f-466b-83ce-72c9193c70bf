﻿<Window x:Class="TartanLogApp.NetListenWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:TartanLogApp"
        mc:Ignorable="d" ResizeMode="CanMinimize" Closed="Window_Closed"
        Title="数据接收" Height="680" Width="600">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="50"/>
            <RowDefinition />
        </Grid.RowDefinitions>
        <Image Source="ThemeResource\tartanlogo2.png" Stretch="Uniform" HorizontalAlignment="Left" Height="30" Margin="10,0"/>
        <TextBlock FontSize="22" FontWeight="Black" VerticalAlignment="Center" Grid.ColumnSpan="2" HorizontalAlignment="Center">数据接收</TextBlock>
        <GroupBox Grid.Row="1" BorderBrush="Black" Margin="20,10" Width="380" Height="150" VerticalAlignment="Top" HorizontalAlignment="Left" Header="远端服务器设置" FontSize="16">
            <Grid Margin="10">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" Height="50">
                        <TextBlock Width="30" FontSize="16" VerticalAlignment="Center" Text="端口"  Margin="5"/>
                        <TextBox x:Name="tbPort" Width="80"  Height="30" Margin="10,0" Padding="5" FontSize="16" TextAlignment="Center" VerticalAlignment="Center"/>
                        <Ellipse x:Name="lblStatus" Height="10" Fill="Red" Width="10"/>
                        <TextBlock Width="80" TextAlignment="Center" FontSize="14" VerticalAlignment="Center" Text="字符编码" Margin="20,5,5,5"/>
                        <ComboBox x:Name="cbCharset" SelectedIndex="0" Width="80" Height="25" FontSize="12" VerticalAlignment="Center" >
                            <ComboBoxItem Content="UTF-8" />
                            <ComboBoxItem Content="Unicode" />
                            <ComboBoxItem Content="ANSI" />
                            <ComboBoxItem Content="GB2312" />
                        </ComboBox>                        
                    </StackPanel>
                    <StackPanel Orientation="Horizontal" >
                        <Border BorderThickness="1" BorderBrush="Gray" Width="130" Height="40" Margin="10,0">
                            <StackPanel Orientation="Horizontal" Height="40">
                                <RadioButton x:Name="rbTCP" IsChecked="True" Content="TCP" FontSize="12" VerticalAlignment="Center" VerticalContentAlignment="Center" Margin="10,0,0,0"/>
                                <RadioButton x:Name="rbUDP" Content="UDP" FontSize="12" VerticalAlignment="Center" VerticalContentAlignment="Center" Margin="10,0"/>
                            </StackPanel>
                        </Border>
                        <Button x:Name="btnConnect" Content="连接" Background="AliceBlue" Width="60" Height="30" Margin="40,0,0,0" Click="btnConnect_Click"/>
                        <Button x:Name="btnDisConnect" Content="断开" Background="AliceBlue" Width="60" Height="30" Margin="20,0" Click="btnDisConnect_Click"/>

                    </StackPanel>
                   
                    
                   
                </StackPanel>
            </Grid>
        </GroupBox>
        <StackPanel Grid.Row="1" Margin="20,180,0,0" Width="580" Height="410" VerticalAlignment="Top" HorizontalAlignment="Left" >
            <ListView x:Name="lstRecvData" Margin="0,10,0,0" HorizontalAlignment="Left" VerticalAlignment="Top" Width="300" Height="350">
                <ListView.View>
                    <GridView>
                        <GridViewColumn Header="数据时间" Width="140" DisplayMemberBinding="{Binding Timestamp}" />
                        <GridViewColumn Header="测深" Width="70" DisplayMemberBinding="{Binding Depth}" />
                        <GridViewColumn Header="钻头深" Width="70" DisplayMemberBinding="{Binding BitDepth}" />
                    </GridView>
                </ListView.View>
            </ListView>
            <Button x:Name="btnClear" Width="60" Height="24" BorderBrush="BurlyWood" Background="AliceBlue" FontWeight="Bold" Foreground="LightCoral" HorizontalAlignment="Right" Margin="10,10,30,10" Click="btnClear_Click">清屏</Button>
            <Label x:Name="lblInfo"  FontWeight="Bold" HorizontalAlignment="Left" Margin="10,-24,0,0" />
        </StackPanel>
        <TextBox Grid.Row="1" x:Name="tbSend" Height="350" Margin="330,190,0,0" VerticalScrollBarVisibility="Auto" VerticalAlignment="Top" HorizontalAlignment="Left" Width="240" TextWrapping="Wrap"/>

    </Grid>
</Window>

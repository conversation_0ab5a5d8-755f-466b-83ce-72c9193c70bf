﻿<Window x:Class="TartanLogApp.DataTCPTransWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:TartanLogApp" ResizeMode="CanMinimize" 
        mc:Ignorable="d" Loaded="Window_Loaded" Closed="Window_Closed"
        Title="数据远传" Height="700" Width="1100">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="50"/>
            <RowDefinition />
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>
        <Image Source="ThemeResource\tartanlogo2.png" Stretch="Uniform" HorizontalAlignment="Left" Height="30" Margin="10,0"/>
        <TextBlock FontSize="22" FontWeight="Black" VerticalAlignment="Center" Grid.ColumnSpan="2" HorizontalAlignment="Center">数据远传</TextBlock>
        <GroupBox Grid.Row="1" BorderBrush="Black" Margin="20,10" Width="380" Height="130" HorizontalAlignment="Left" VerticalAlignment="Top" Header="远端服务器设置" FontSize="16">
            <StackPanel>
                    <StackPanel Orientation="Horizontal" Height="50" Margin="10,5">
                        <TextBlock Width="16" FontSize="16" VerticalAlignment="Center" Text="IP"  Margin="5"/>
                        <TextBox x:Name="tbIP" Width="120" Height="30" Margin="10,0" Padding="5" FontSize="16" TextAlignment="Center" VerticalAlignment="Center"/>
                        <TextBlock Width="30" FontSize="16" VerticalAlignment="Center" Text="端口"  Margin="5"/>
                        <TextBox x:Name="tbPort" Width="80"  Height="30" Margin="10,0" Padding="5" FontSize="16" TextAlignment="Center" VerticalAlignment="Center"/>
                        <Ellipse x:Name="lblStatus" Height="10" Fill="Red" Width="10"/>
                    </StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="10,0">
                        <Border BorderThickness="1" BorderBrush="Gray" Width="120" Height="30" Margin="10,0">
                            <StackPanel Orientation="Horizontal" Height="40">
                                <RadioButton x:Name="rbTCP" IsChecked="True" Content="TCP" FontSize="12" VerticalAlignment="Top" VerticalContentAlignment="Center" Margin="10,8,0,0"/>
                                <RadioButton x:Name="rbUDP" Content="UDP" FontSize="12" VerticalAlignment="Top" VerticalContentAlignment="Center" Margin="10,8"/>
                            </StackPanel>
                        </Border>
                        <Button x:Name="btnConnect" Content="连接" Background="AliceBlue" Width="60" Height="30" Margin="40,0,0,0" Click="btnConnect_Click"/>
                        <Button x:Name="btnDisConnect" Content="断开" Background="AliceBlue" Width="60" Height="30" Margin="20,0" Click="btnDisConnect_Click"/>
                    </StackPanel>                    
                </StackPanel>
        </GroupBox>

        <GroupBox Grid.Row="1" BorderBrush="Black" Margin="20,160,0,0" Width="380" Height="340" HorizontalAlignment="Left" VerticalAlignment="Top" Header="WITS传输设置" FontSize="16">
            <Grid >
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="3*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="30" />
                    <RowDefinition Height="30"/>
                    <RowDefinition Height="120" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="50"/>
                </Grid.RowDefinitions>
                <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                    <TextBlock Width="80" FontSize="12" VerticalAlignment="Center" TextAlignment="Center"  Text="传输项目" Margin="5,5,5,0"/>
                    <TextBlock Width="80" FontSize="12" VerticalAlignment="Center" TextAlignment="Center" Text="WITS Code" Margin="5,5,0,0"/>
                    <TextBlock Width="80" FontSize="12" VerticalAlignment="Center" TextAlignment="Center" Text="井深WITS" Margin="0,5,0,0"/>
                </StackPanel>

                <StackPanel Orientation="Horizontal" Grid.Row="1">
                    <ComboBox x:Name="cbGvs" Width="80" Height="25" FontSize="12" VerticalAlignment="Top" Margin="5,0"/>
                    <TextBox x:Name="tbWits"  Width="70" Height="25" Text="1234" FontSize="12" VerticalAlignment="Top" TextAlignment="Center" Padding="3" Margin="10,0,0,0"/>
                    <TextBox x:Name="tbWitsDepth" Width="70" Text="0" Height="25" FontSize="12" VerticalAlignment="Top" TextAlignment="Center" Padding="3" Margin="10,0"/>
                </StackPanel>
                
                <Button x:Name="btnAddWits" Content="添加" Background="AliceBlue" Width="60" Height="30" Grid.Column="1" Grid.RowSpan="2" Margin="0" Click="btnAddWits_Click"/>
                <Button x:Name="btnWitDelete" Grid.Row="2" VerticalAlignment="Top" Margin="0,40" Grid.Column="1" Content="删除" Background="AliceBlue" Width="60" Height="30" Click="btnWitDelete_Click"/>
                <Button x:Name="btnWitsClear" Grid.Row="2" VerticalAlignment="Bottom" Margin="0,0,0,10"  Grid.Column="1" Content="清空" Background="AliceBlue" Width="60" Height="30" Click="btnWitsClear_Click"/>
                <ListBox x:Name="listWits"  Grid.Row="2" Margin="5,5,5,10" FontSize="12" />
                <!--<RichTextBox x:Name="rtbWitsList" Grid.Row="2" Margin="5,5,5,10" FontSize="12" IsReadOnly="True" VerticalScrollBarVisibility="Auto">
                    <FlowDocument LineHeight="1">
                        <Paragraph />
                    </FlowDocument>
                </RichTextBox>-->
               
                <StackPanel Grid.Row="3" Grid.ColumnSpan="2" Margin="250,0,0,0">
                    <TextBlock Width="80" TextAlignment="Center" FontSize="14" VerticalAlignment="Center" Text="字符编码" Margin="5"/>
                    <ComboBox x:Name="cbCharset" SelectedIndex="0" Width="80" Height="25" FontSize="12" VerticalAlignment="Top">
                        <ComboBoxItem Content="UTF-8" />
                        <ComboBoxItem Content="Unicode" />
                        <ComboBoxItem Content="ANSI" />
                        <ComboBoxItem Content="GB2312" />
                    </ComboBox>
                </StackPanel>
                
                <StackPanel Grid.Row="3" Margin="15,0">
                    <StackPanel Orientation="Horizontal" Height="40">
                        <TextBlock Width="100" FontSize="14" VerticalAlignment="Center" Text="起始发送井深" Margin="5"/>
                        <TextBox x:Name="tbStartDepth" Width="100"  Height="30" Padding="5" FontSize="16" TextAlignment="Center" VerticalAlignment="Center"/>
                    </StackPanel>
                    <StackPanel Orientation="Horizontal" Height="40">
                        <TextBlock Width="100" FontSize="14" VerticalAlignment="Center" Text="延迟深度(m)" Margin="5"/>
                        <TextBox x:Name="tbDepthDelay" Width="100"  Height="30" Padding="5" FontSize="16" TextAlignment="Center" VerticalAlignment="Center"/>
                    </StackPanel>
                </StackPanel>

                <Border BorderBrush="DarkGray" BorderThickness="0,1,0,0" Margin="5,2,10,0" Grid.Row="4" Grid.ColumnSpan="2"/>
           
                <Border BorderBrush="DarkGray" BorderThickness="0,1,0,0" Margin="5,118,10,0" Grid.Row="2" Grid.ColumnSpan="2"/>

                <StackPanel Orientation="Horizontal" Grid.Row="4">
                    <Button x:Name="btnSettingSend" Content="开始发送" Background="AliceBlue" Width="80" Height="30" Margin="40,0,0,0" Click="btnSettingSend_Click"/>
                    <Button x:Name="btnSendStop" Content="停止发送" Background="AliceBlue" Width="80" Height="30" Margin="40,0,0,0" Click="btnSendStop_Click"/>
                </StackPanel>

            </Grid>
        </GroupBox>

        <TextBlock x:Name="tbLastSentLabel" Grid.Row="1" VerticalAlignment="Bottom" Height="100" Margin="80,0,80,30"/>
        <TextBlock Grid.Row="1" Grid.Column="1" Text="已发送数据" Foreground="#FF46BD63" FontSize="15" FontWeight="Bold" VerticalAlignment="Top" Height="30" Margin="0,5"/>
        <StackPanel Orientation="Horizontal" Grid.Row="1" Grid.Column="1" HorizontalAlignment="Right" VerticalAlignment="Top" >
            <Button x:Name="btnlstClear" Width="48" Height="24" BorderBrush="BurlyWood" Background="AliceBlue" FontWeight="Bold" Foreground="BurlyWood" VerticalAlignment="Bottom" Click="btnlstClear_Click">清空</Button>
            <Button x:Name="btnlstSave" Width="48" Height="24" BorderBrush="DarkCyan" Background="AliceBlue" FontWeight="Bold" Foreground="DarkCyan" Margin="20,0,20,0" VerticalAlignment="Bottom" Click="btnlstSave_Click">保存</Button>
        </StackPanel>
        <ListView x:Name="lstSentData" Grid.Row="1" Grid.Column="1"  Margin="0,30,0,50" HorizontalAlignment="Left" Width="660">
            <ListView.View>
                <GridView>
                    <GridViewColumn Header="发送时间" Width="130" DisplayMemberBinding="{Binding SendTime}" />
                    <GridViewColumn Header="测深" Width="70" DisplayMemberBinding="{Binding MeasureDepth}" />
                    <GridViewColumn Header="标签" Width="70" DisplayMemberBinding="{Binding TagName}" />
                    <GridViewColumn Header="数值" Width="70" DisplayMemberBinding="{Binding OrignalVal}" />
                    <GridViewColumn Header="数据时间" Width="130" DisplayMemberBinding="{Binding Timestamp}" />
                    <GridViewColumn Header="传输数据" Width="180" DisplayMemberBinding="{Binding SendString}" />
                </GridView>
            </ListView.View>
        </ListView>
    </Grid>
</Window>

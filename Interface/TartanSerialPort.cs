﻿using System;
using System.Collections.Generic;
using System.IO.Ports;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace TartanLogApp.Interface
{

    public delegate void PortStatusCallback(bool open, string portName, string fwversion);

    public class TartanSerialPort
    {
        private SerialPort m_serialPort;
        private PortStatusCallback portStatusCallback;
        private const int MAX_BUFFER_SIZE = 1024;
        private const int MAX_RETRIES = 3;
        private const int SERIAL_READ_TIMEOUT = 500;
        private const int SERIAL_WRITE_TIMEOUT = 500;

        //等待Response响应
        private const int STEP_INTERVAL_IN_MS = 10;
        private const int MAX_INTERVAL_IN_MS = 100;

        private int nMaxCount = MAX_INTERVAL_IN_MS / STEP_INTERVAL_IN_MS;

        private byte[] bufferIn;
        private int LenInput;
        private bool waitingforData;

        public Exception Exception { get; set; }
        public string Port => m_serialPort.PortName;
        public bool IsOpen => m_serialPort.IsOpen;


        public void SetPortStatusCallback(PortStatusCallback statusCallback)
        {
            portStatusCallback = statusCallback;
        }

        public TartanSerialPort()
        {
            m_serialPort = new SerialPort();
            m_serialPort.WriteTimeout = SERIAL_WRITE_TIMEOUT;
            m_serialPort.ReadTimeout = SERIAL_READ_TIMEOUT;
            m_serialPort.DataReceived += m_serialPort_DataReceived;

            portStatusCallback = null;
            bufferIn = new byte[MAX_BUFFER_SIZE];
            LenInput = 0;
            waitingforData = false;

        }

        private void m_serialPort_DataReceived(object sender, SerialDataReceivedEventArgs e)
        {
            if (!waitingforData)
            {
                LenInput = 0;
                return;
            }

            int nCount = m_serialPort.BytesToRead;
            if (nCount < 1)
            {
                return;
            }

            if ((nCount + LenInput) <= MAX_BUFFER_SIZE)
            {
                byte[] byteArray = new byte[nCount];
                try
                {
                    if (m_serialPort.Read(byteArray, 0, nCount) == nCount)
                    {
                        Buffer.BlockCopy(byteArray, 0, bufferIn, LenInput, nCount);
                        LenInput += nCount;
                    }
                }
                catch (Exception ex)
                {

                    throw ex;
                }

            }

        }

        public async void Open(string portName)
        {
            await this.Open(SerialPortConfig.GetDefault(portName));

        }

        public async Task Open(SerialPortConfig config)
        {
            if (m_serialPort.IsOpen)
            {
               //

                portStatusCallback?.Invoke(true, config.PortName, "FirmwareVersion");

                return;

            }

            SetSerialCfg(config);

            await Task.Factory.StartNew(() => {

                try
                {
                    m_serialPort.Open();
                }
                catch (Exception e)
                {
                    Exception = e;
                }

            });

            if (m_serialPort.IsOpen)
            {
                //
                portStatusCallback?.Invoke(true, config.PortName, "FirmwareVersion");

            }

        }

        public void Close()
        {
            if (m_serialPort.IsOpen)
            {
                try
                {
                    m_serialPort.Close();

                }
                catch (Exception e)
                {
                    Exception = e;
                }

                if (!m_serialPort.IsOpen)
                {
                   
                    portStatusCallback?.Invoke(false, "Closed", "");
                    
                }
            }
        }

        public async Task<SerialResponse> SendCmdAsyn(SerialRequest request)
        {
            return await Task.Factory.StartNew(() => {

                SerialResponse response;
                int retry = 0;
                do
                {                    
                    response = SendCmd(request);
                    retry++;
                } while (!response.IsSucceed && (retry < MAX_RETRIES));

                return response;

            });
        }

        public SerialResponse SendCmd(SerialRequest request)
        {
            SerialResponse response=new SerialResponse();

            byte[] bytes = request.Bytes;
            
            int Len = request.ExpectedLen;
            byte[] data = new byte[Len];

            if (!IsOpen)
            {
                response.Exception = new Exception("PortClosed");
                return response;
            }
            //
            lock (this)
            {
                m_serialPort.DiscardInBuffer();
                m_serialPort.DiscardOutBuffer();

                try
                {
                    m_serialPort.Write(bytes, 0, bytes.Length);
                }
                catch (Exception x)
                {
                    Exception = x;
                    response.Exception = x;
                    return response;
                }

                request.TimeStamp = DateTime.Now;
                waitingforData = true;
                LenInput = 0;
                

                int nCount = 0;
                do
                {
                    Thread.Sleep(STEP_INTERVAL_IN_MS);
                    nCount++;
                } while ((LenInput < Len) && (nCount < nMaxCount));

                response.TimeStamp = DateTime.Now;

                waitingforData = false;
        

                if (LenInput < Len)
                {
                    Buffer.BlockCopy(bufferIn, 0, data, 0, LenInput);
                    response.Exception = new TimeoutException("Timeout");

                }
                else
                {
                    Buffer.BlockCopy(bufferIn, LenInput - Len, data, 0, Len);
                                  
                }

               // response.SetReceivedData(data);

                return response;
            }

        }

        private void SetSerialCfg(SerialPortConfig config)
        {
            if (string.IsNullOrEmpty(config.PortName))
            {
                Exception = new Exception("Invalid Port");
                return;
            }
            //Port= config.PortName;
            m_serialPort.PortName = config.PortName;
            m_serialPort.BaudRate = config.BaudRate;
            m_serialPort.DataBits = config.DataBits;
            m_serialPort.Parity = config.Parity;
            m_serialPort.StopBits = config.StopBits;
            m_serialPort.Handshake = config.Handshake;
        }

    }

    public class SerialPortConfig
    {
        public string PortName { get; set; }
        public int BaudRate { get; set; }
        public int DataBits { get; set; }
        public StopBits StopBits { get; set; }
        public Handshake Handshake { get; set; }
        public Parity Parity { get; set; }
        public int ReadTimeout { get; set; }

        public static SerialPortConfig GetDefault(string port)
        {
            return new SerialPortConfig()
            {
                PortName = port,
                BaudRate = 115200,
                DataBits = 8,
                StopBits = StopBits.One,
                Handshake = Handshake.None,
                Parity = Parity.None
            };
        }
        public static SerialPortConfig GetDefault()
        {
            return GetDefault(string.Empty);
        }

        public static List<string> GetPortNames()
        {
            return SerialPort.GetPortNames().ToList();
        }

        public static List<int> GetBaudRates()
        {
            return new List<int>()
            {
                300,
                600,
                1200,
                2400,
                4800,
                9600,
                19200,
                38400,
                57600,
                115200,
                230400,
                460800,
                921600
            };
        }

        public static List<int> GetDataBits()
        {
            return new List<int>() { 5, 6, 7, 8 };
        }

    }
}

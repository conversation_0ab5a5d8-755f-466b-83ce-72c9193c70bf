﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TartanLogApp.Interface
{
    public abstract class SerialMessage
    {
        public Exception Exception {  get; set; }

        public virtual DateTime TimeStamp { get; set; }
        public virtual string Message { get; set; }

        public virtual byte[] Bytes { get; }
        public virtual bool IsRequest { get; }
        public virtual bool IsSucceed { get; }
      
    }
}

﻿# Visual Studio 生成的文件和文件夹
bin/
obj/
packages/
.vs/

# 编译输出
*.exe
*.dll
*.pdb
*.cache
*.suo
*.user
*.userosscache
*.sln.docstates

# Visual Studio 用户特定文件
*.sln.DotSettings.user
*.csproj.user
*.vbproj.user
*.vcxproj.user

# 构建结果
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
[Ww][Ii][Nn]32/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
bld/
[Ll]og/
[Ll]ogs/

# MSTest 测试结果
[Tt]est[Rr]esult*/
[Bb]uild[Ll]og.*

# NUnit
*.VisualState.xml
TestResult.xml
nunit-*.xml

# NuGet 包
*.nupkg
*.snupkg

# ReSharper
_ReSharper*/
*.[Rr]e[Ss]harper
*.DotSettings.user

# JetBrains Rider
.idea/
*.sln.iml

# Windows 系统文件
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.lnk

# macOS 系统文件
.DS_Store
.AppleDouble
.LSOverride

# 临时文件
*.tmp
*.temp
*.log
*.swp
*.swo
*~

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 备份文件
*.bak
*.backup

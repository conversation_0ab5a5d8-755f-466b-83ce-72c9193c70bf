﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TartanLogApp.DataSource
{
    public enum DataLogType
    {
        Depth = 0,
        Time,
        TVD
    }

    public enum LogDataUnits
    {
        Metric = 0,
        Imperial
    }

    public class LASFile
    {
        private const int DEPTH_CONVERT_VALUE = 1000;
        //private const long TICKS_TO_SECONDS = 10000000;

        private string mFilename;

        private LogDataUnits mLogUnits;

        private float mStartDepth;
        private float mStopDepth;
        private float mStep;
        private float mNullValue;
        private string mCompany;
        private string mWell;
        private string mField;
        private string mLocation;
        private string mProvince;
        private string mCounty;
        private string mState;
        private string mCountry;
        private string mServiceCompany;
        private string mDate;
        private string mUWI;
        private string mAPINumber;
        private string mLicenseNumber;

        private int mNumberOfItems;
        private bool[] mExportItems;
        private string[] mItemMnemonics;
        private string[] mItemDecriptions;

        private long[] mDepth;
        private float[,] mData;


        public LASFile()
        {
            mCompany = "";
            mWell = "";
            mField = "";
            mLocation = "";
            mProvince = "";
            mCounty = "";
            mState = "";
            mCountry = "";
            mServiceCompany = "";
            mDate = "";
            mUWI = "";
            mAPINumber = "";
            mLicenseNumber = "";
        }

        public string Filename
        {
            get { return mFilename; }
            set { mFilename = value; }
        }

        public LogDataUnits LogUnits
        {
            get { return mLogUnits; }
            set { mLogUnits = value; }
        }

        public float StartDepth
        {
            get { return mStartDepth; }
            set { mStartDepth = value; }
        }

        public float StopDepth
        {
            get { return mStopDepth; }
            set { mStopDepth = value; }
        }

        public float Step
        {
            get { return mStep; }
            set { mStep = value; }
        }

        public float NullValue
        {
            get { return mNullValue; }
            set { mNullValue = value; }
        }

        public string Company
        {
            get { return mCompany; }
            set { mCompany = value; }
        }

        public string Well
        {
            get { return mWell; }
            set { mWell = value; }
        }

        public string Field
        {
            get { return mField; }
            set { mField = value; }
        }

        public string Location
        {
            get { return mLocation; }
            set { mLocation = value; }
        }

        public string Province
        {
            get { return mProvince; }
            set { mProvince = value; }
        }

        public string County
        {
            get { return mCounty; }
            set { mCounty = value; }
        }

        public string State
        {
            get { return mState; }
            set { mState = value; }
        }

        public string Country
        {
            get { return mCountry; }
            set { mCountry = value; }
        }

        public string ServiceCompany
        {
            get { return mServiceCompany; }
            set { mServiceCompany = value; }
        }

        public string Date
        {
            get { return mDate; }
            set { mDate = value; }
        }

        public string UWI
        {
            get { return mUWI; }
            set { mUWI = value; }
        }

        public string APINumber
        {
            get { return mAPINumber; }
            set { mAPINumber = value; }
        }

        public string LicenseNumber
        {
            get { return mLicenseNumber; }
            set { mLicenseNumber = value; }
        }

        public int NumberOfItems
        {
            get { return mNumberOfItems; }
            set { mNumberOfItems = value; }
        }

        public void SetExportItems(bool[] vExportItems)
        {
            mExportItems = vExportItems;
        }

        public void SetMnemonics(string[] vMnemonics)
        {
            mItemMnemonics = vMnemonics;
        }

        public string[] GetDecriptions()
        {
            return mItemDecriptions;
        }

        public void SetDecriptions(string[] vDecriptions)
        {
            mItemDecriptions = vDecriptions;

            mNumberOfItems = mItemDecriptions.Length;
        }

        public long[] GetDepth()
        {
            return mDepth;
        }

        public void SetDepth(long[] vDepth)
        {
            mDepth = vDepth;
        }

        public float[,] GetData()
        {
            return mData;
        }

        public void SetData(float[,] vData)
        {
            mData = vData;
        }

        private void WriteVersionInformation(StreamWriter sw)
        {
            sw.WriteLine("~VERSION INFORMATION");

            sw.WriteLine("VERS.    2.0                    : CWLS LOG ASCII STANDARD - VERSION 2.0");
            sw.WriteLine("WRAP.    NO                     : ONE LINE PER DEPTH STEP");
        }

        private void WriteWellInfomation(StreamWriter sw)
        {
            try
            {
                string sLogUnits;

                if (mLogUnits == LogDataUnits.Imperial)
                    sLogUnits = "FT";
                else
                    sLogUnits = "M";

                sw.WriteLine("~WELL INFORMATION");

                sw.WriteLine("STRT." + sLogUnits + "    " + mStartDepth.ToString("F3").PadRight(20) + " : START DEPTH");
                sw.WriteLine("STOP." + sLogUnits + "    " + mStopDepth.ToString("F3").PadRight(20) + " : STOP DEPTH");
                sw.WriteLine("STEP." + sLogUnits + "    " + mStep.ToString().PadRight(20) + " : STEP");
                sw.WriteLine("NULL.      " + mNullValue.ToString().PadRight(20) + " : NULL VALUE");
                sw.WriteLine("COMP.      " + mCompany.PadRight(20) + " : COMPANY");
                sw.WriteLine("WELL.      " + mWell.PadRight(20) + " : WELL");
                sw.WriteLine("FLD.       " + mField.PadRight(20) + " : FIELD");
                sw.WriteLine("LOC.       " + mLocation.PadRight(20) + " : LOCATION");
                sw.WriteLine("PROV.      " + mProvince.PadRight(20) + " : PROVINCE");
                sw.WriteLine("CNTY.      " + mCounty.PadRight(20) + " : COUNTY");
                sw.WriteLine("STAT.      " + mState.PadRight(20) + " : STATE");
                sw.WriteLine("CTRY.      " + mCountry.PadRight(20) + " : COUNTRY");
                sw.WriteLine("SRVC.      " + mServiceCompany.PadRight(20) + " : SERVICE COMPANY");
                sw.WriteLine("DATE.      " + mDate.PadRight(20) + " : DATE");
                sw.WriteLine("UWI.       " + mUWI.PadRight(20) + " : UNIQUE WELL ID");
                sw.WriteLine("API.       " + mAPINumber.PadRight(20) + " : API NUMBER");
                sw.WriteLine("LIC.       " + mLicenseNumber.PadRight(20) + " : LICENCE NUMBER");
            }
            catch (Exception ex)
            {
                //UtilityClass.LogExceptions(ex, "LASClass.WriteWellInformation");
            }
        }

        private void WriteCurveInfomation(StreamWriter sw)
        {
            try
            {
                string sLogUnits;

                if (mLogUnits == LogDataUnits.Imperial)
                    sLogUnits = "FT";
                else
                    sLogUnits = "M";

                sw.WriteLine("~CURVE INFORMATION");


                sw.WriteLine("DEPTH." + sLogUnits.PadRight(25) + " :  DEPTH");

                for (int i = 0; i < mNumberOfItems; i++)
                {
                    if (mExportItems[i] == true)
                        sw.WriteLine(mItemMnemonics[i].Replace(" ", "").Trim() + ".".PadRight(22) + " :  " + mItemDecriptions[i]);
                }
            }
            catch (Exception ex)
            {
                //UtilityClass.LogExceptions(ex, "LASClass.WriteCurveInformation");
            }
        }

        private void WriteLogDataInfomation(StreamWriter sw)
        {
            try
            {
                string sHeader = "~A ";

                sHeader += "DEPTH ".PadRight(9);

                for (int i = 0; i < mNumberOfItems; i++)
                {
                    if (mExportItems[i] == true)
                        sHeader += mItemMnemonics[i].Replace(" ", "").Trim().PadRight(12) + " ";
                }

                sw.WriteLine(sHeader);

                string sRecord;

                for (int i = 0; i < mDepth.Length; i++)
                {
                    if ((mDepth[i] >= (mStartDepth * DEPTH_CONVERT_VALUE)) && (mDepth[i] <= (mStopDepth * DEPTH_CONVERT_VALUE)))
                    {
                        sRecord = (mDepth[i] / (float)DEPTH_CONVERT_VALUE).ToString("F3").PadRight(12) + " ";

                        for (int j = 0; j < mNumberOfItems; j++)
                        {
                            if (mExportItems[j] == true)
                                sRecord += mData[j, i].ToString("0.00").PadRight(12) + " ";
                        }

                        sw.WriteLine(sRecord);
                    }
                }
            }
            catch (Exception ex)
            {
                //UtilityClass.LogExceptions(ex, "LASClass.WriteLogDataInformation");
            }

        }

        public void SaveLASFile()
        {
            try
            {
                StreamWriter sw = new StreamWriter(mFilename);

                WriteVersionInformation(sw);

                WriteWellInfomation(sw);

                WriteCurveInfomation(sw);

                WriteLogDataInfomation(sw);

                sw.Close();
            }
            catch (Exception ex)
            {
                //UtilityClass.LogExceptions(ex, "LASClass.SaveLASFile");
            }
        }

        private string ReadVersionInformation(StreamReader sr, string vLine)
        {
            string sLine = vLine;

            try
            {
                if ((vLine.Contains("~V")) && (!sr.EndOfStream))
                {
                    sLine = sr.ReadLine();

                    while (sLine[0] != '~')
                    {
                        sLine = sr.ReadLine();
                    }
                }
            }
            catch (Exception ex)
            {
                //UtilityClass.LogExceptions(ex, "LASClass.ReadVersionInformation");
            }

            return sLine;
        }

        private string ReadWellInformation(StreamReader sr, string vLine)
        {
            string sLine = vLine;

            try
            {
                if ((vLine.Contains("~W")) && (!sr.EndOfStream))
                {
                    sLine = sr.ReadLine();

                    while (sLine[0] != '~')
                    {
                        if (sLine[0] != '#')
                        {
                            string sData = sLine.Substring(sLine.IndexOf('.') + 1, sLine.IndexOf(':') - sLine.IndexOf('.') - 1).Trim();

                            if (sLine.Substring(0, 4) == "STRT")
                            {
                                if ((sData[0] == 'M') || (sData[0] == 'm'))
                                {
                                    mLogUnits = LogDataUnits.Metric;
                                    sData = sData.Replace('M', ' ');
                                    sData = sData.Replace('m', ' ');
                                }
                                else
                                {
                                    mLogUnits = LogDataUnits.Imperial;
                                    sData = sData.Replace('F', ' ');
                                    sData = sData.Replace('f', ' ');
                                    sData = sData.Replace('T', ' ');
                                    sData = sData.Replace('t', ' ');
                                }

                                sData = sData.Trim();

                                mStartDepth = Convert.ToSingle(sData);
                            }
                            else if (sLine.Substring(0, 4) == "STOP")
                            {
                                sData = sData.Replace('M', ' ');
                                sData = sData.Replace('m', ' ');
                                sData = sData.Replace('F', ' ');
                                sData = sData.Replace('f', ' ');
                                sData = sData.Replace('T', ' ');
                                sData = sData.Replace('t', ' ');
                                sData = sData.Trim();

                                mStopDepth = Convert.ToSingle(sData);
                            }
                            else if (sLine.Substring(0, 4) == "STEP")
                            {
                                sData = sData.Replace('M', ' ');
                                sData = sData.Replace('m', ' ');
                                sData = sData.Replace('F', ' ');
                                sData = sData.Replace('f', ' ');
                                sData = sData.Replace('T', ' ');
                                sData = sData.Replace('t', ' ');
                                sData = sData.Trim();

                                double dTest;
                                bool bTest;

                                bTest = Double.TryParse(sData, out dTest);
                                if (bTest == true)
                                    mStep = Convert.ToSingle(sData);
                                else
                                    mStep = 0.0F;
                            }
                            else if (sLine.Substring(0, 4) == "NULL")
                                mNullValue = Convert.ToSingle(sData);
                            else if (sLine.Substring(0, 4) == "COMP")
                                mCompany = sData;
                            else if (sLine.Substring(0, 4) == "WELL")
                                mWell = sData;
                            else if (sLine.Substring(0, 3) == "FLD")
                                mField = sData;
                            else if (sLine.Substring(0, 3) == "LOC")
                                mLocation = sData;
                            else if (sLine.Substring(0, 4) == "PROV")
                                mProvince = sData;
                            else if (sLine.Substring(0, 4) == "CNTY")
                                mCounty = sData;
                            else if (sLine.Substring(0, 4) == "STAT")
                                mState = sData;
                            else if (sLine.Substring(0, 4) == "CTRY")
                                mCountry = sData;
                            else if (sLine.Substring(0, 4) == "SRVC")
                                mServiceCompany = sData;
                            else if (sLine.Substring(0, 4) == "DATE")
                                mDate = sData;
                            else if (sLine.Substring(0, 3) == "UWI")
                                mUWI = sData;
                            else if (sLine.Substring(0, 3) == "API")
                                mAPINumber = sData;
                            else if (sLine.Substring(0, 3) == "LIC")
                                mLicenseNumber = sData;


                        }

                        sLine = sr.ReadLine();
                    }
                }
            }
            catch (Exception ex)
            {
                //UtilityClass.LogExceptions(ex, "LASClass.ReadWellInformation");
            }

            return sLine;
        }

        private string ReadCurveInformation(StreamReader sr, string vLine)
        {
            string sLine = vLine;

            try
            {
                if ((vLine.Contains("~C")) && (!sr.EndOfStream))
                {
                    List<string> lstDataItems = new List<string>();
                    lstDataItems.Clear();
                    mNumberOfItems = 0;

                    sLine = sr.ReadLine();

                    while (sLine[0] != '~')
                    {
                        if (sLine[0] != '#')
                        {
                            string sData = sLine.Substring(0, sLine.IndexOf('.')).Trim();
                            lstDataItems.Add(sData);
                            mNumberOfItems++;
                        }
                        sLine = sr.ReadLine();
                    }

                    // To remove depth column
                    lstDataItems.RemoveAt(0);
                    mNumberOfItems--;

                    mItemDecriptions = lstDataItems.ToArray();
                }
            }
            catch (Exception ex)
            {
                //UtilityClass.LogExceptions(ex, "LASClass.ReadCurveInformation");
            }

            return sLine;
        }

        private string ReadLogDataInformation(StreamReader sr, string vLine)
        {
            string sLine = vLine;

            try
            {
                if (vLine.Contains("~A"))
                {
                    int nNumberOfRecords = 0;

                    List<string[]> lstData = new List<string[]>();
                    lstData.Clear();

                    //sLine = sr.ReadLine();

                    //while ((sLine[0] != '~') && (!sr.EndOfStream))
                    while ((!sr.EndOfStream))
                    {
                        sLine = sr.ReadLine();

                        if ((sLine[0] != '~') && (sLine[0] != '#'))
                        {
                            sLine = sLine.Replace('\t', ' ').Trim();

                            while (sLine.Contains("  ") == true)
                                sLine = sLine.Replace("  ", " ");

                            string[] sData = sLine.Split(' ');

                            lstData.Add(sData);

                            nNumberOfRecords++;
                        }

                        //sLine = sr.ReadLine();
                    }

                    mDepth = new long[nNumberOfRecords];
                    mData = new float[mNumberOfItems, nNumberOfRecords];

                    for (int i = 0; i < nNumberOfRecords; i++)
                    {
                        string[] sData = lstData[i];

                        mDepth[i] = Convert.ToInt64(Convert.ToSingle(sData[0]) * DEPTH_CONVERT_VALUE);

                        for (int j = 0; j < mNumberOfItems; j++)
                        {
                            mData[j, i] = Convert.ToSingle(sData[j + 1]);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                //UtilityClass.LogExceptions(ex, "LASClass.ReadLogDataInformation");
            }

            return sLine;
        }

        public bool OpenLASFile()
        {
            bool bRetVal = true;

            try
            {
                StreamReader sr = new StreamReader(mFilename);

                string sLine = sr.ReadLine();

                // To skip past any comments at the beginning of the log
                while ((sLine[0] != '~') && (!sr.EndOfStream))
                {
                    sLine = sr.ReadLine();
                }

                if (sr.EndOfStream == false)
                {
                    sLine = ReadVersionInformation(sr, sLine);
                    sLine = ReadWellInformation(sr, sLine);
                    sLine = ReadCurveInformation(sr, sLine);
                    sLine = ReadLogDataInformation(sr, sLine);
                }
                else
                {
                    bRetVal = false;
                }

                sr.Close();
            }
            catch (Exception ex)
            {
                //UtilityClass.LogExceptions(ex, "LASClass.OpenLASFile");
            }

            return bRetVal;
        }
    }
}

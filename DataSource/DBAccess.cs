﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Data.SQLite;
using System.Data.Common;
using System.Data.Odbc;

namespace TartanLogApp.DataSource
{
    internal class DBAccess
    {
        private string dataSource;
        private SQLiteConnection connection;
        private SQLiteCommand command;
        private SQLiteDataReader reader;
       
        public DBAccess(string path)
        {
            try
            {
                dataSource = path;
                connection = new SQLiteConnection(dataSource);
                connection.Open();
            }
            catch (Exception e)
            {
                throw e;
            }
        }

        public void Close()
        {
            command?.Cancel();
            command = null;
           
            reader?.Close();
            reader = null;

            connection?.Close();
            connection = null;
        }

        private SQLiteDataReader ExecuteQuery(string queryString)
        {
            try
            {
                command = connection.CreateCommand();
                command.CommandText = queryString;
                reader = command.ExecuteReader();
            }
            catch (Exception e)
            {
                System.Diagnostics.Debug.WriteLine(e.Message);
            }

            return reader;
        }

        private int ExecuteNoneQuery(string sql)
        {
            int result = 0;
            try
            {
                command = connection.CreateCommand();
                command.CommandText = sql;
                result = command.ExecuteNonQuery();
            }
            catch (Exception e)
            {
                System.Diagnostics.Debug.WriteLine(e.Message);
            }

            return result;
        }

        public void CreateTable(string tableName, string[] colNames, string[] colTypes)
        {
            string queryString = "CREATE TABLE IF NOT EXISTS " + tableName + "( " + colNames[0] + " " + colTypes[0];
            for (int i = 1; i < colNames.Length; i++)
            {
                queryString += ", " + colNames[i] + " " + colTypes[i];
            }
            queryString += "  ) ";
            ExecuteNoneQuery(queryString);
        }

        public SQLiteDataReader ReadTable(string tableName, string[] items, string[] colNames, string[] operations, string[] colValues)
        {
            string queryString = "SELECT " + items[0];
            for (int i = 1; i < items.Length; i++)
            {
                queryString += ", " + items[i];
            }
            queryString += " FROM " + tableName + " WHERE " + colNames[0] + " " + operations[0] + " " + colValues[0];
            for (int i = 0; i < colNames.Length; i++)
            {
                queryString += " AND " + colNames[i] + " " + operations[i] + " " + colValues[0] + " ";
            }
            return ExecuteQuery(queryString);
        }
        
        //$"INSERT INTO test(testId,tdate) VALUES(23,unixepoch())";
        public int InsertValues(string tableName, string[] values)
        {
            string queryString = "INSERT INTO " + tableName + " VALUES (" + "'" + values[0] + "'";
            for (int i = 1; i < values.Length; i++)
            {
                queryString += ", " + "'" + values[i] + "'";
            }
            queryString += " )";
            return ExecuteNoneQuery(queryString);
        }

        public DataTable FromTable(string sql)
        {
            DataTable dt = new DataTable();
            
            command = connection.CreateCommand();
            command.CommandText = sql;

            SQLiteDataAdapter dataAdapter = new SQLiteDataAdapter(command);
            
            dataAdapter.Fill(dt);

            dataAdapter.Dispose();

            return dt;
        }

        public int Update(string tableName, string colName, string newValue, string conditions)
        {
            string queryString = $"UPDATE {tableName} SET {colName} = '{newValue}' WHERE {conditions} ";
            return ExecuteNoneQuery(queryString);
        }

        public int Delete(string tableName, string conditions)
        {
            string queryString = $"DELETE FROM {tableName} WHERE {conditions} ";
            return ExecuteNoneQuery(queryString);
        }

        public int Update(string tableName, string[] colNames, string[] newValues, string conditions)
        {
            string queryString = $"UPDATE {tableName} SET {colNames[0]}='{newValues[0]}'";
            for (int i = 1; i < colNames.Length; i++)
            {
                queryString += $", {colNames[i]}='{newValues[i]}'";
            }
            queryString += " WHERE " + conditions;
            return ExecuteNoneQuery(queryString);
        }

    }
}

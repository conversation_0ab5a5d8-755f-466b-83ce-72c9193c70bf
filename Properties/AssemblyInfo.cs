﻿using System.Reflection;
using System.Resources;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Windows;

// 有关程序集的一般信息由以下
// 控制。更改这些特性值可修改
// 与程序集关联的信息。
[assembly: AssemblyTitle("TartanLogApp")]
[assembly: AssemblyDescription("")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("Tartan")]
[assembly: AssemblyProduct("TartanLogApp")]
[assembly: AssemblyCopyright("Copyright ©  2023")]
[assembly: AssemblyTrademark("")]
[assembly: AssemblyCulture("")]

// 将 ComVisible 设置为 false 会使此程序集中的类型
//对 COM 组件不可见。如果需要从 COM 访问此程序集中的类型
//请将此类型的 ComVisible 特性设置为 true。
[assembly: ComVisible(false)]

//若要开始生成可本地化的应用程序，请设置
//.csproj 文件中的 <UICulture>CultureYouAreCodingWith</UICulture>
//例如，如果您在源文件中使用的是美国英语，
//使用的是美国英语，请将 <UICulture> 设置为 en-US。  然后取消
//对以下 NeutralResourceLanguage 特性的注释。  更新
//以下行中的“en-US”以匹配项目文件中的 UICulture 设置。

//[assembly: NeutralResourcesLanguage("en-US", UltimateResourceFallbackLocation.Satellite)]


[assembly: ThemeInfo(
    ResourceDictionaryLocation.None, //主题特定资源词典所处位置
                                     //(未在页面中找到资源时使用，
                                     //或应用程序资源字典中找到时使用)
    ResourceDictionaryLocation.SourceAssembly //常规资源词典所处位置
                                              //(未在页面中找到资源时使用，
                                              //、应用程序或任何主题专用资源字典中找到时使用)
)]


// 程序集的版本信息由下列四个值组成: 
//
//      主版本
//      次版本
//      生成号
//      修订号
//
//可以指定所有这些值，也可以使用“生成号”和“修订号”的默认值
//通过使用 "*"，如下所示:
// [assembly: AssemblyVersion("1.0.*")]
[assembly: AssemblyVersion("1.0.0.17")]
[assembly: AssemblyFileVersion("1.0.0.17")]

//
//  2025-7-25
//**** V1.0.0.17
//WITS列表可单条删除
//换ListBox
//
//
//  2025-7-23
//**** V1.0.0.16
//添加BitRun选择
//
//

//  2025-7-10
//**** V1.0.0.15
//修复测斜数据未发送井深 MDpth
//测斜数据深度改用井深 = Survey表中的测深 + 零长
//Gama 改回原来逻辑
//
//
//  2025-7-8
//**** V1.0.0.14
//远传增加 井深 = Survey表中的测深 + 零长
//原来传输的都是测深
//Gama 传输用测深，不减零长
//
//  2025-5-20
//**** V1.0.0.12
//远传TCP服务器中断时，添加自动重连 
//更新mezDB ResultsSet表
//
//
//  2025-5-17
//**** V1.0.0.11
//接收录井钻头深度数据 更新mezDB
//
//
//  2025-5-15
//**** V1.0.0.9
//发送数据改用NetConnectSevice
//
//
//
//
//  2023-8-31
//**** V1.0.0.8
//记录设置config.xml，启动后使用最后保存的设置
//
//
//  2023-8-18
//**** V1.0.0.7
//成像最大最小设置，设置一次后不再重置读取
//
//
//  2023-8-14
//**** V1.0.0.7
//优化曲线和成像显示，超过4000个点隔一个点显示
//
//
//  2023-8-13
//**** V1.0.0.6
//MeztlDB数据导入速度优化，导入导出删除功能
//Gama添加系数 Gamma Scale Factor
//
//
//  2023-8-4
//**** V1.0.0.6
//添加导出Mezintel数据功能
//测斜数据发送问题修复，重复发送问题，最后一条数据问题
//不能停止发送修复，异步改同步 BeginInvoke => Invoke
//
//
//  2023-7-31
//**** V1.0.0.5
//远传配合中海油山西煤层气 WITS Server 接收数据用监听方式给发送
//测斜和伽马数据分开发送
//
//  2023-7-26
//**** V1.0.0.4
//数据远传完成 TCP/UDP 可监听接收
//去掉零长选择，曲线平滑插值可选
//
//  2023-7-19
//**** V1.0.0.3
//导出图片完成
//数据远传测试
//
//  2023-7-13
//**** V1.0.0.2
//2扇区4扇区方位Gamma成像完成
//数据插值，解决三次样条插值在各别大波动区间出现结果跳跃问题，采用跳跃区间采用线性插值
//
//  2023-7-6
//**** V1.0.0.1
//最初版本发布，Gamma曲线显示，mezintel数据集显示
//
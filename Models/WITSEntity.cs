﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TartanLogApp.Models
{
    public class WITSEntity
    {
        private List<witsItem> witsItems;
        public int Count => (HasWellNm? witsItems.Count-1: witsItems.Count);
        public bool HasWellNm { get; private set; }
        public bool HasSurvey { get; private set; }
        public bool IncSend = false;
        public bool AzmSend = false;

        public bool MDpthSend = false;

        public WITSEntity()
        {
            witsItems=new List<witsItem>();
            HasWellNm = false;
            HasSurvey = false;
        }

        public bool AddWits(witsItem item)
        {
            if (!witsItems.Select(o=>o.GvName).ToList().Contains(item.GvName))
            {
                if (item.GvName == "WellName")
                {
                    HasWellNm = true;
                }
                if (item.GvName == "MDpth")
                {
                    MDpthSend = true;
                }
                if (item.GvName == "Inc")
                {
                    HasSurvey = true;
                    IncSend = true;
                }
                if (item.GvName == "TAzm")
                {
                    HasSurvey = true;
                    AzmSend = true;
                }
                witsItems.Add(item);
                return true;
            }
            return false;
        }

        public void WitsClear()
        {
            witsItems.Clear();
            HasWellNm = false;
            HasSurvey = false;
            IncSend = false;
            AzmSend = false;
            MDpthSend = false;
        }
        public void WitsDel(string gv)
        {
            witsItem item = witsItems.FirstOrDefault(o => o.GvName == gv);
            bool ret = witsItems.Remove(item);
            if (ret)
            {
                if (gv == "WellName")
                {
                    HasWellNm = false;
                }
                if (gv == "MDpth")
                {
                    MDpthSend = false;
                }
                if (gv == "Inc")
                {
                    IncSend = false;
                }
                if (gv == "TAzm")
                {
                    AzmSend = false;
                }
                if (!IncSend && !AzmSend)
                {
                    HasSurvey = false;
                }
            }
            
        }
        public List<string> GetWitsGVs()
        {
            List<string> strings = new List<string>();
            foreach (witsItem item in witsItems)
            {
                if(item.GvName != "WellName")
                    strings.Add(item.GvName);
            }            
            return strings;
        }
        public witsItem GetWitsCode(string gv)
        {
            return witsItems.FirstOrDefault(o => o.GvName == gv);
        }
    }

    public class witsItem
    {
        public string GvName;
        public int WitsCode;
        public int DepthWits;
    }

}

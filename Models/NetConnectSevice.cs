﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Sockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using TartanLogApp.DataSource;

namespace TartanLogApp.Models
{
    public enum ConnectType
    {
        TCP,
        UDP
    }

    class NetConnectSevice
    {
        private string server;
        private int port;
        private TcpClient client;
        private UdpClient UdpClient;
        private ConnectType connectType;
        public ConnectType ConnectType => connectType;
        public bool IsConnected { get; private set; }
        public Action<List<TransItem>> OnSendEvent = null;
        static int RECV_BUFF_LEN = 8192;
        public NetConnectSevice(string server, int port, ConnectType type)
        {
            IsConnected = false;
            this.server = server;
            this.port = port;
            this.connectType = type;
        }

        public void Connect()
        {
            try
            {
                if (connectType == ConnectType.TCP)
                {
                    client = new TcpClient(server, port);
                    System.Diagnostics.Debug.WriteLine($"Connected! TCP {server}:{port}");
                    
                }
                else
                {
                    UdpClient = new UdpClient(server, port);
                    System.Diagnostics.Debug.WriteLine($"Connect Setup! UDP {server}:{port}");
                }
                Thread.Sleep(200);
                IsConnected = true;
            }
            catch (Exception)
            {
                IsConnected = false;
                System.Diagnostics.Debug.WriteLine("Connecting Failed!");
            }

        }

        public void Close()
        {
            if (connectType == ConnectType.TCP)
                client?.Close();
            else
                UdpClient?.Close();
        }

        public void Send(string head, List<TransItem> items, WITSEntity wits, Encoding encoding)
        {
            if (!IsConnected)
            {
                System.Diagnostics.Debug.WriteLine("NOT CONNECTED!");
               
                //try re connect
                Connect();
                Thread.Sleep(200);
               
                if (!IsConnected)
                {
                    MessageBox.Show("连接失败，发送失败，请手动重连！");
                    return;
                }
            }
           
            byte[] buff = new byte[RECV_BUFF_LEN];
            string message = head;
            //有效数据项
            int k = 0;           
            foreach (TransItem item in items)
            {
                witsItem wits1 = wits.GetWitsCode(item.GvName);
                if (item.Depth > 0)
                {
                    k++;
                    message += wits1.WitsCode.ToString("0000") + item.RetVal.ToString("0.00") + "\r\n";
                    message += wits1.DepthWits.ToString("0000") + item.Depth.ToString("0.00") + "\r\n";
                }
            }
            if (k == 0)
            {
                return;
            }
            message += "!!\r\n";
            byte[] data = encoding.GetBytes(message);

            if (connectType == ConnectType.TCP)
            {
                if ((client == null)|| !client.Connected )                    
                {
                    IsConnected = false;
                    return;
                }

                try
                {
                    NetworkStream stream = client.GetStream();
                    stream.Write(data, 0, data.Length);
                    /*
                    if (stream.CanRead)
                    {
                        while(stream.DataAvailable)
                        {
                            int cnt = stream.Read(buff, 0, RECV_BUFF_LEN);
                            System.Diagnostics.Debug.WriteLine(encoding.GetString(buff,0,cnt));
                        }
                        
                    }
                    */
                    OnSendEvent?.Invoke(items);
                }
                catch (Exception)
                {
                    System.Diagnostics.Debug.WriteLine("Send Failed!");
                }

            }
            else
            {
                if (UdpClient == null)
                {
                    IsConnected = false;
                    return;
                }

                int len = 0;

                for (int i = 0; i < 3; i++)
                {
                    try
                    {
                        len = UdpClient.Send(data, data.Length);
                    }
                    catch (Exception)
                    {
                        len = 0;
                       // throw;
                    }

                    if (len <= 0)
                        System.Threading.Thread.Sleep(20);
                    else
                        break;
                }
                if (len > 0)
                    OnSendEvent?.Invoke(items);
                else
                    System.Diagnostics.Debug.WriteLine("Send Failed!");

            }
        }

    }
}

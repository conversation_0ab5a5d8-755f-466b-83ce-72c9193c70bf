using System;

namespace TartanLogApp.Models
{
    /// <summary>
    /// 日志数据类 - 从NetListenWindow.xaml.cs中提取出来用于最小化验证
    /// </summary>
    public class LoggingData
    {
        public DateTime dateTime;
        public double Depth;
        public double BitDepth;
        public static double LastBitDepth;
        
        public LoggingData()
        {
            this.dateTime = DateTime.MinValue;
            this.Depth = 0.0;
            this.BitDepth = 0.0;
            LastBitDepth = 0;
        }
        
        public override string ToString()
        {
            return string.Format("{0}\t{1:N2}\t{2:N2}", this.dateTime.ToString("G"), this.Depth, this.BitDepth);
        }
    }
}

﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.XPath;
using System.Xml;

namespace TartanLogApp.Models
{
    internal static class Util
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="fileName"></param>
        /// <param name="lines"></param>
        public static void SaveCSV(string fileName, List<CSVRow> lines)
        {
            using (var sw = new System.IO.StreamWriter(fileName, false))
            {
                //sw.WriteLine(header);
                foreach (var item in lines)
                {
                    sw.WriteLine(item.ToCSV());
                }
            }
            return;
        }

        #region XML操作

        public static XmlElement CreateXmlElement(XmlNode parent, string name, string attributeName, string attributeValue, object val)
        {
            XmlElement xmlElement = parent.OwnerDocument.CreateElement(name);
            if (attributeName != null && attributeValue != null)
            {
                XmlAttribute xmlAttribute = parent.OwnerDocument.CreateAttribute(attributeName);
                xmlAttribute.Value = attributeValue;
                xmlElement.Attributes.Append(xmlAttribute);
            }
            if (val != null)
            {
                string text;
                if (val.GetType().Equals(typeof(float)) || val.GetType().Equals(typeof(double)))
                {
                    text = string.Format("{0:r}", val);
                }
                else
                {
                    text = val.ToString();
                }
                XmlText xmlText = parent.OwnerDocument.CreateTextNode(text);
                xmlElement.AppendChild(xmlText);
            }
            parent.AppendChild(xmlElement);
            return xmlElement;
        }

        public static XmlElement CreateXmlElement(XmlNode parent, string name, object val)
        {
            return CreateXmlElement(parent, name, null, null, val);
        }

        public static XmlElement CreateXmlElement(XmlNode parent, string name)
        {
            return CreateXmlElement(parent, name, null, null, null);
        }

        public static string GetFirstXmlNodeValue(XPathNavigator nav, string path)
        {
            XPathNodeIterator xpathNodeIterator = (XPathNodeIterator)nav.Evaluate(path + "/text()");
            if (xpathNodeIterator.Count < 1)
            {
                return "";
            }
            xpathNodeIterator.MoveNext();
            return xpathNodeIterator.Current.Value;
        }

        public static string GetXmlNodeAttribute(XPathNavigator nav, string path, string attr)
        {
            XPathNodeIterator xpathNodeIterator = (XPathNodeIterator)nav.Evaluate(path);
            if (xpathNodeIterator.Count < 1)
            {
                return "";
            }
            xpathNodeIterator.MoveNext();
            return xpathNodeIterator.Current.GetAttribute(attr,"");
        }

        #endregion

        #region 插值及数据处理

        /// <summary>
        /// 
        /// </summary>
        /// <param name="x0"></param>
        /// <param name="y0"></param>
        /// <param name="x"></param>
        /// <param name="ret"></param>
        /// <param name="smooth"></param>
        /// <returns></returns>
        public static bool Interpolate(double[] x0, double[] y0, ref double[] x, out double[] ret, bool smooth=true )
        {
            double[] depth, value;
            int n = Datafilter(x0, y0, out depth, out value);
            ret = new double[x.Length];

            if (n == 0) return false;

            x = x.OrderBy(o => o).ToArray();
           /*
            System.Diagnostics.Debug.WriteLine("######################");
            for (int i = 0; i < n; i++)
            {
                System.Diagnostics.Debug.WriteLine($"{depth[i]}\t{value[i]}");
            }
            */
            if (smooth)
            {
                return CubicSplineInterpolate(depth, value, x, ref ret);
            }
            else
            {
                return PiecewiseLagrange(depth, value, x, ref ret, 1);
            }
            //return PiecewiseLagrange(depth, value, x, ref ret);
        }

        /// <summary>
        /// 三次样条插值方法
        /// </summary>
        /// <param name="depth">升序无重复值数组</param>
        /// <param name="value"></param>
        /// <param name="x">升序数组</param>
        /// <param name="results"></param>
        /// <returns></returns>
        private static bool CubicSplineInterpolate(double[] depth, double[] value, double[] x, ref double[] results)
        {

            int N = depth.Length - 1;

            //1 计算微商，构造三对角矩阵
            double[] h = new double[N];
            double[] g = new double[N + 1];
            double[] M = new double[N + 1];

            double[] b = new double[N + 1];
            double[] L = new double[N + 1];
            double[] U = new double[N + 1];
            double[] Y = new double[N + 1];

            h[0] = depth[1] - depth[0];
            for (int i = 1; i < N; i++)
            {
                h[i] = depth[i + 1] - depth[i];
                g[i] = 6 * ((value[i + 1] - value[i]) / h[i] - (value[i] - value[i - 1]) / h[i - 1]);
                b[i] = 2 * (h[i] + h[i - 1]);
            }

            //边界条件 两端点一阶导数为0
            g[0] = 6 * (value[1] - value[0]) / h[0];
            g[N] = -6 * (value[N] - value[N - 1]) / h[N - 1];
           
            //  两端点二阶导数为0
            g[0] = 0;
            g[N] = 0;


            //2 LU分解

            b[0] = 2 * h[0];
            b[N] = 2 * b[N - 1];

            U[0] = b[0];

            for (int i = 1; i <= N; i++)
            {
                L[i] = h[i - 1] / U[i - 1];
                U[i] = b[i] - L[i] * h[i - 1];
            }

            //3 LY=g

            Y[0] = g[0];
            for (int i = 1; i <= N; i++)
            {
                Y[i] = g[i] - L[i] * Y[i - 1];
            }

            //4 UM=Y
            M[N] = Y[N] / U[N];
            for (int i = N - 1; i >= 0; i--)
            {
                M[i] = (Y[i] - h[i] * M[i + 1]) / U[i];
            }

            //
            double[] ai = new double[N];
            double[] ci = new double[N];

            for (int i = 0; i < N; i++)
            {
                ai[i] = (M[i + 1] - M[i]) / 6 / h[i];
                ci[i] = (value[i + 1] - value[i]) / h[i] - (2 * M[i] * h[i] + M[i + 1] * h[i]) / 6;
            }

            //
            int k = 1;
            bool leap = false;
            for (int i = 0; i < x.Length; i++)
            {
                //边界以外按边界值
                if (x[i] <= depth[0])
                {
                    results[i] = value[0];
                }
                else if (x[i] >= depth[N])
                {
                    results[i] = value[N];
                }
                else
                {

                    if (leap)
                    {
                        //如果超出范围，区间内全部线性插值
                        results[i] = (value[k + 1] - value[k]) * (x[i] - depth[k]) / (depth[k+1] - depth[k]) + value[k];
                        
                        //越过区间，退出线性
                        if (x[i] > depth[k + 1])
                        {
                            leap = false;
                        }
                    }
                    else
                    {
                        //找区间
                        for (int j = k; j <= N; j++)
                        {
                            if (x[i] < depth[j])
                            {
                                k = j - 1; break;
                            }
                        }
                        //样条插值
                        results[i] = ai[k] * (x[i] - depth[k]) * (x[i] - depth[k]) * (x[i] - depth[k]) + M[k] / 2 * (x[i] - depth[k]) * (x[i] - depth[k]) + ci[k] * (x[i] - depth[k]) + value[k];

                        //判断插值结果是否超两端数据点
                        if (results[i] < Math.Min(value[k], value[k + 1]) || results[i] > Math.Max(value[k], value[k + 1]))
                        {
                            int min, max;
                            min = k - 2;
                            if (min < 0)
                                min = 0;

                            max = min + 5;

                            if (max > N)
                            {
                                max = N;
                                min = max - 5;
                            }
                            var piece = value.Skip(min).Take(6).ToList();
                            double d = piece.Max() - piece.Min();
                            //如果样条插值结果在前后各3个数据点区间内最大最小值加极差/2 范围以外，做标记，区间内全部采用线性插值
                            if (results[i] < piece.Min() - d/2 || results[i] > piece.Max() + d/2)
                            {
                                while (x[i] > depth[k])
                                {
                                    i--;
                                    if (i < 0)
                                    {                                        
                                        break;
                                    }
                                }
                                leap = true;
                            }
                        }
                    }
                                        
                }
                
            }
            /*
            System.Diagnostics.Debug.WriteLine("*****************************");
            for (int i = 0; i < x.Length; i++)
            {
                System.Diagnostics.Debug.WriteLine(x[i] + "\t" + results[i]);
            }
            */
            return true;
        }

        /// <summary>
        /// 分段低次插值方法
        /// </summary>
        /// <param name="depth">升序无重复值数组</param>
        /// <param name="value"></param>
        /// <param name="x">升序数组</param>
        /// <param name="results"></param>
        /// <param name="ideg">次数</param>
        /// <returns></returns>
        private static bool PiecewiseLagrange(double[] depth, double[] value, double[] x, ref double[] results, int ideg = 2)
        {
            int N = depth.Length - 1;
            int k = 1;
            for (int i = 0; i < x.Length ; i++)
            {
                                
                //边界以外按边界值
                if (x[i] <= depth[0])
                {
                    results[i] = value[0];
                }
                else if (x[i] >= depth[N])
                {
                    results[i] = value[N];
                }
                else
                {
                    //找相邻的位置
                    for (int j = k; j <= N; j++)
                    {
                        if (x[i] < depth[j])
                        {
                            k = j - 1; break;
                        }
                    }
                    int MinIndex, MaxIndex;
                    double temp, factor;
                    MaxIndex = k + 1 + ideg / 2;
                    if (MaxIndex > N)
                    {
                        MaxIndex = N;
                    }
                    MinIndex = MaxIndex - ideg;
                    if (MinIndex < 0)
                    {
                        MinIndex = 0;
                        MaxIndex = MinIndex + ideg;                        
                    }
                    temp = 0;
                    for (int m = MinIndex; m <= MaxIndex; m++)
                    {
                        if (x[i] != depth[m])
                        {
                            factor = value[m];
                            for (int j = MinIndex; j <= MaxIndex; j++)
                            {
                                if (j != m)
                                    factor *= (x[i] - depth[j]) / (depth[m] - depth[j]);
                            }
                            temp += factor;
                        }
                        else
                        {
                            results[i] = value[m];
                            temp = value[m];
                            break;
                        }
                    }
                    results[i] = temp;
                }

                //System.Diagnostics.Debug.WriteLine(x[i] + "\t" + results[i]);
            }
            /*
            System.Diagnostics.Debug.WriteLine("------------------------------");
            for (int i = 0; i < x.Length; i++)
            {
                System.Diagnostics.Debug.WriteLine(x[i] + "\t" + results[i]);
            }
            */
            return true;
        }

        /// <summary>
        /// 数据预处理，去掉重复值
        /// </summary>
        /// <param name="x"></param>
        /// <param name="y"></param>
        /// <param name="depth"></param>
        /// <param name="gr"></param>
        /// <returns></returns>
        private static int Datafilter(double[] x, double[] y, out double[] depth, out double[] gr)
        {
            int N = x.Length;

            //至少4个点插值

            depth = new double[N];
            gr = new double[N];

            if ((N < 4) || (N != y.Length))
            {
                return 0;
            }

            List<XYDataPoint> points = new List<XYDataPoint>();

            for (int i = 0; i < N; i++)
            {
                points.Add(new XYDataPoint { X = x[i], Y = y[i] });
            }

            points.Sort((u, w) => u.X.CompareTo(w.X));

            for (int i = 0; i < N - 1; i++)
            {
                depth[i] = points[i].X;
                gr[i] = points[i].Y;
                
                //System.Diagnostics.Debug.WriteLine(depth[i] + "\t" + gr[i]);

                if (points[i].X == points[i + 1].X)
                {
                    points.Remove(points[i + 1]);
                    N--; i--;
                }
            }

            if (N < 4)
            {
                return 0;
            }

            depth[N - 1] = points[N - 1].X;
            gr[N - 1] = points[N - 1].Y;

            depth = depth.Take(N).ToArray();
            gr = gr.Take(N).ToArray();

            return N;
        }

        #endregion

        private class XYDataPoint
        {
            public double X; public double Y;
        }


    }

    public class CSVRow
    {
        private List<object> m_values;

        public CSVRow()
        {
            m_values = new List<object>();
        }

        public void Add(object value)
        {
            m_values.Add(value);
        }

        public void AddRange(IEnumerable<object> values)
        {
            foreach (object value in values)
                m_values.Add(value);
        }

        public string ToCSV()
        {
            return m_values.ToCSV();
        }
    }

    public static class ExtensionMethods
    {
        public static string ToCSV(this List<object> itemList)
        {
            return string.Join(",", itemList);
        }

        public static string ToCSV(this List<string> itemList)
        {
            return string.Join(",", itemList);
        }
    }
}

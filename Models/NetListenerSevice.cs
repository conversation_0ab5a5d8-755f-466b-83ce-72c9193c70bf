﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Mime;
using System.Net.Sockets;
using System.Net;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Net.Http;
using TartanLogApp.DataSource;
using System.Windows;

namespace TartanLogApp.Models
{
    internal class NetListenerSevice
    {
        private TcpListener _listener;
        private UdpClient _udpClient;
        private int _port;
        private string _host;
        private ConnectType _connectType;

        public ConnectType ConnectType => _connectType;
        public bool IsStartListening;

        public Action<byte[], int> OnRecvEvent = null;
        static int RECV_BUFF_LEN = 8192;

        private CancellationTokenSource m_cts;
        private TcpClient _tcpClient;
        public bool IsConnected { get; private set; }
        public Action<List<TransItem>> OnSendEvent = null;

        public NetListenerSevice(ConnectType connectType, int port)
        {
            _connectType = connectType;
            _port = port;
            IsStartListening = false;
        }
        public void StartListen()
        {
            //this.m_cts = cts;
            //Listening();
            Task.Factory.StartNew(() =>
            {
                Listening();
            });
        }

        private void Listening()
        {
            IPAddress ipAddress = IPAddress.Any; //Dns.GetHostAddresses("127.0.0.1")[0];
            m_cts = new CancellationTokenSource();
            byte[] bytes = new byte[RECV_BUFF_LEN];
            string data;

            if (_connectType == ConnectType.TCP)
            {
                // Enter the listening loop.
                //TcpClient client;
                _listener = new TcpListener(ipAddress, this._port);

                _listener.Start();
                IsStartListening = true;
                IsConnected = true;
                while (!m_cts.Token.IsCancellationRequested)
                {
                    //System.Diagnostics.Debug.Write("Waiting for a connection... ");
                    
                    if (_listener.Pending())
                    {
                        _tcpClient = _listener.AcceptTcpClient();
                        //_listener.Server.SendTimeout = 10;
                        //System.Diagnostics.Debug.WriteLine("Connected!");
                        //_tcpClient = client;
                        //IsConnected = true;
                        //IsStartListening = true;
                        Thread.Sleep(400);
                    }
                    else
                    {
                        continue;
                    }
                    
                    data = null;                    
                    // Get a stream object for reading and writing
                    NetworkStream stream = _tcpClient.GetStream();
                    int i;
                    // Loop to receive all the data sent by the client.
                    while ((i = stream.Read(bytes, 0, bytes.Length)) != 0)
                    {
                        // Translate data bytes to a ASCII string.
                        //data = Encoding.UTF8.GetString(bytes, 0, i);
                        //System.Diagnostics.Debug.WriteLine($"Received: \n{data}");
                        OnRecvEvent?.Invoke(bytes, i);
                        bytes = new byte[RECV_BUFF_LEN];
                    }
                    
                    //client?.Close();
                }
               
                IsConnected = false;
                IsStartListening = false;
                _tcpClient?.Close(); 
                _listener.Stop();                
                _listener.Server.Close();
            }
            else
            {
                _udpClient = new UdpClient(_port);
                IPEndPoint RemoteIpEndPoint = new IPEndPoint(IPAddress.Any, _port);
                IsStartListening = true;

                while (!m_cts.Token.IsCancellationRequested)
                {
                    System.Diagnostics.Debug.Write("Waiting for a connection... ");

                    byte[] udpbytes = _udpClient.Receive(ref RemoteIpEndPoint);
                    data = Encoding.UTF8.GetString(udpbytes, 0, udpbytes.Length);
                    System.Diagnostics.Debug.WriteLine($"UDP Received: \n{data}");

                    OnRecvEvent?.Invoke(udpbytes, udpbytes.Length);
                }

                _udpClient.Close();
            }

        }

        public void Send(string head, List<TransItem> items, WITSEntity wits, Encoding encoding)
        {
            if (!IsConnected)
            {
                System.Diagnostics.Debug.WriteLine("NOT CONNECTED!");
                MessageBox.Show("连接失败，发送失败，请手动重连！");
                return;
            }

            byte[] buff = new byte[RECV_BUFF_LEN];
            string message = head;

            foreach (TransItem item in items)
            {
                witsItem wits1 = wits.GetWitsCode(item.GvName);
                message += wits1.WitsCode.ToString("0000") + item.RetVal.ToString("0.00") + "\r\n";
                message += wits1.DepthWits.ToString("0000") + item.Depth.ToString("0.00") + "\r\n";                
            }

            message += "!!\r\n";
            byte[] data = encoding.GetBytes(message);

            if (_connectType == ConnectType.TCP)
            {
                if ((_tcpClient == null) || !_tcpClient.Connected)
                {
                    IsConnected = false;
                    return;
                }

                try
                {
                    NetworkStream stream = _tcpClient.GetStream();
                    stream.Write(data, 0, data.Length);
                    /*
                    if (stream.CanRead)
                    {
                        while (stream.DataAvailable)
                        {
                            int cnt = stream.Read(buff, 0, RECV_BUFF_LEN);
                            System.Diagnostics.Debug.WriteLine(encoding.GetString(buff, 0, cnt));
                        }
                    }
                    */
                    OnSendEvent?.Invoke(items);
                }
                catch (Exception)
                {
                    System.Diagnostics.Debug.WriteLine("Send Failed!");
                }
            }
            else
            {
                //udp is not available yet.
            }
        }

        public void Stop()
        {
            //_listener?.Stop();
            
            //_udpClient.Close();
            m_cts?.Cancel();
            //_listener?.Stop();
            //_tcpClient?.Close();
            IsStartListening = false;
            
        }
    }
}

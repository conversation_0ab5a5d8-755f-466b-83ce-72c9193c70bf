﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Threading;
using TartanLogApp.DataSource;
using TartanLogApp.CurveControl;
using TartanLogApp.Models;
using System.Windows.Ink;
using System.Data;
using static TartanLogApp.DataSource.MeztlDB;
using System.Reflection;
using System.IO;
using Microsoft.Win32;
using System.Windows.Media.Animation;

namespace TartanLogApp
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : Window
    {
        private DispatcherTimer timer;
        private List<string> GRList;
        private MeztlDB meztlDB;
        private long jobId;
        private int bitRunNo;
        private string wellName;
        public static ImageSource logo;
        private bool smooth;

        private DataTCPTransWindow transWindow;
        private NetListenWindow listenWindow;

        public MainWindow()
        {
            InitializeComponent();
            timer= new DispatcherTimer();
            GRList = new List<string>();
            bitRunNo = -1;
            timer.Tick += timer_Tick;
        }

        private void timer_Tick(object sender, EventArgs e)
        {
            UpdateChart();
            UpdateDataSet();
        }
        
        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            tbWell.Text= string.Empty;
            tbGRlist.Text= string.Empty;
            lblStatus.Fill = Brushes.Red;
            ckSmooth.IsChecked = true;
            smooth = true;
            tbRefresh.Text = "60";
            timer.Interval = TimeSpan.FromSeconds(60);
            //btnImgExport.IsEnabled = false;
            btnDataTrans.IsEnabled = false;
            btnNetListen.IsEnabled = false;
            btnSaveJob.IsEnabled = false;
            Curve.ClearCanvas();
            ClearSector();
            logo = imglogo.Source;
            this.Title += " - V" + Assembly.GetExecutingAssembly().GetName().Version;
            UserSettings.LoadXML("config.xml");

            if (UserSettings.Instance.JobID > 0)
            {
                tbJobID.Text = UserSettings.Instance.JobID.ToString();
                tbWellNm.Text = UserSettings.Instance.Wellname;
                //ckSurvey.IsChecked = UserSettings.Instance.survey;
                ckSmooth.IsChecked = UserSettings.Instance.smooth;
                tbRefresh.Text = UserSettings.Instance.Interval.ToString();
            }

            //var upload = new DataUpload();
            //BitRunsData bitRuns = new BitRunsData { bitRunId = 1234, jobId = 2626, run = 1, wellNumber = "测试" };
            //upload.Publish(DataUpload.dataType.VL_BIT_RUNS, bitRuns);
            //string s= upload.GetJobID("塞392H27-2井");
            
        }

        private void btnJobConnect_Click(object sender, RoutedEventArgs e)
        {           
            if (meztlDB == null)
            {
                try
                {
                    jobId = long.Parse(tbJobID.Text.Trim());
                    meztlDB = new MeztlDB(jobId, true);
                    //meztlDB = new MeztlDB(jobId, ckSurvey.IsChecked.Value);
                }
                catch (Exception)
                {
                    return;
                }
            }
            if (meztlDB.WellName != null)
            {
                lblStatus.Fill= Brushes.Lime;
                tbWell.Text= meztlDB.WellName;
                
                if (string.IsNullOrEmpty(tbWellNm.Text.Trim()))
                    tbWellNm.Text= meztlDB.WellName;

                wellName = tbWellNm.Text.Trim();

                cbGRs.Items.Clear();
                tbGRlist.Text=string.Empty;
                GRList.Clear();
                foreach (string grtagName in meztlDB.GetGRTagNames())
                {
                    if (grtagName.StartsWith("GV")|| grtagName.StartsWith("Ga"))
                        cbGRs.Items.Add(grtagName);                    
                }
                cbGRs.SelectedIndex = 0;

                //25.7.23 添加趟钻选择
                string run = cbRuns.Text.Trim();
                if (run == "All")
                {
                    run = "-1";
                }
                try
                {
                    if (!string.IsNullOrEmpty(run))
                    {
                        bitRunNo = int.Parse(run);
                        meztlDB.CurBitRun = bitRunNo;
                    }                    
                }
                catch (Exception)
                {
                    return;
                }                
                
                btnJobConnect.IsEnabled = false;
                tbJobID.IsEnabled = false;
                //ckSurvey.IsEnabled = false;
                tbWellNm.IsEnabled = false;
                btnDataTrans.IsEnabled = true;
                btnNetListen.IsEnabled = true;
                btnSaveJob.IsEnabled = true;
                
                UserSettings.LoadXML("config.xml");
                LoadSettings();

            }
        }

        private void LoadSettings()
        {
            if (jobId == UserSettings.Instance.JobID)
            {
                if (UserSettings.Instance.CurveCount > 0)
                {
                    foreach (string gr in UserSettings.Instance.GRList)
                    {
                        meztlDB.AddGR(gr);
                        GRList.Add(gr);
                        tbGRlist.Text += " " + gr;
                    }
                    meztlDB.ReadFeaturesVal();
                    UpdateChart();
                    Curve.UpdateChart();

                    if (UserSettings.Instance.GRList.Count > 1)
                    {
                        putSectors();
                        if (UserSettings.Instance.GRList.Count > 3)
                        {
                            putSectors(4);
                        }
                    }

                    if (UserSettings.Instance.GammaImging)
                    {
                        string[] grs = new string[UserSettings.Instance.sector];
                        cbSector.Text = UserSettings.Instance.sector.ToString();
                        grs[0] = UserSettings.Instance.tagNameToAzm["上"];
                        grs[1] = UserSettings.Instance.tagNameToAzm["下"];
                        cbUp.Text = grs[0];
                        cbDown.Text = grs[1];
                        if (UserSettings.Instance.sector > 2)
                        {
                            grs[2] = UserSettings.Instance.tagNameToAzm["左"];
                            grs[3] = UserSettings.Instance.tagNameToAzm["右"];
                            cbLeft.Text = grs[2];
                            cbRight.Text = grs[3];
                        }
                        Curve.SetGammaImg(grs);
                        RefreshCurve();
                    }
                    UpdateDataSet();
                    timer.Start();
                }
                UserSettings.Instance.GRList = GRList;
            }
            else
            {
                UserSettings.Instance.JobID = jobId;
                UserSettings.Instance.Wellname = wellName;
                UserSettings.Instance.GRList = GRList;
                //UserSettings.Instance.survey = ckSurvey.IsChecked.Value;
                UserSettings.Instance.survey = true;
                ChartSettings.Reset();
                UserSettings.Instance.GammaImging = false;
                UserSettings.Instance.DataTrans = false;
            }
        }
      
        private void btnDataTrans_Click(object sender, RoutedEventArgs e)
        {
            if (transWindow == null || transWindow.Tag != null)
            {
                //if (bitRunNo == -1)
                //{
                //    MessageBox.Show("先选择趟钻！");
                //    return;
                //}
                transWindow = new DataTCPTransWindow(jobId, bitRunNo, wellName);
            }
            transWindow.Show();
            transWindow.Activate();
        }

        private void btnNetListen_Click(object sender, RoutedEventArgs e)
        {
            if (listenWindow == null || listenWindow.Tag != null)
                listenWindow = new NetListenWindow(jobId);
            listenWindow.Show();
            listenWindow.Activate();
        }

        private void btnJobDisConnect_Click(object sender, RoutedEventArgs e)
        {
            UserSettings.SaveXML("config.xml");

            meztlDB = null;
            tbWell.Text = string.Empty;
            tbGRlist.Text = string.Empty;
            GRList.Clear();
            cbGRs.Items.Clear();
            wellName = "";
            //ChartSettings.set = false;
            //ChartSettings.serieSettings.Clear();
            timer.Stop(); 
            Curve.ClearCanvas();
            UserSettings.Instance.sector = 0;
            UserSettings.Instance.GammaImging = false;
            UserSettings.Instance.tagNameToAzm.Clear();
            ChartSettings.Reset();

            lblStatus.Fill = Brushes.Red;

            btnJobConnect.IsEnabled = true;
            tbJobID.IsEnabled = true;           
            //ckSurvey.IsEnabled = true;
            tbWellNm.IsEnabled = true;
            btnDataTrans.IsEnabled = false;
            btnNetListen.IsEnabled = false;
            btnSaveJob.IsEnabled = false;
            ClearSector();
        }

        private void btnGRClear_Click(object sender, RoutedEventArgs e)
        {
            tbGRlist.Text= string.Empty;
            GRList.Clear();
            timer.Stop();
            if (meztlDB != null)
            {
                meztlDB.ClearGR();
            }
            Curve.ClearCanvas();
            ChartSettings.Reset();
            UserSettings.Instance.sector = 0;
            UserSettings.Instance.GammaImging = false;
            UserSettings.Instance.tagNameToAzm.Clear();
            ClearSector();
        }

        private void ClearSector()
        {
            cbSector.SelectedIndex = 0;
            cbSector.IsEnabled = false;
            
            cbUp.Items.Clear();
            cbUp.IsEnabled = false;
            cbDown.Items.Clear();
            cbDown.IsEnabled = false;
            cbLeft.Items.Clear();
            cbLeft.IsEnabled = false;
            cbRight.Items.Clear();
            cbRight.IsEnabled = false;
        }

        private void putSectors(int k = 2)
        {
            cbSector.IsEnabled = true;

            putGammas(cbUp);
            putGammas(cbDown);

            if (k == 4)
            {
                putGammas(cbLeft);
                putGammas(cbRight);
            }            
        }

        private void putGammas(ComboBox combo)
        {
            combo.Items.Clear();
            foreach (string gr in GRList)
            {
                combo.Items.Add(gr);
            }
            combo.IsEnabled = true;
            combo.SelectedIndex = 0;
        }

        private void btnInterval_Click(object sender, RoutedEventArgs e)
        {
            int interval;
            try
            {
                interval = int.Parse(tbRefresh.Text.Trim());
            }
            catch (Exception)
            {
                return ;
            }

            timer.Stop();
            timer.Interval=TimeSpan.FromSeconds(interval);
            
            UserSettings.Instance.Interval = interval;

            if (GRList.Count > 0)
                timer.Start();
        }

        private void btnAddGR_Click(object sender, RoutedEventArgs e)
        {
            string gr = cbGRs.Text.Trim();
            if (!string.IsNullOrEmpty(gr))
            {
                if (!GRList.Contains(gr))
                {
                    int ret = meztlDB.AddGR(gr);
                    if (ret == -1)
                    {
                        //MessageBox.Show(gr + " 缺少Offset, Scale 特征值");
                        //return;
                    }
                    GRList.Add(gr);
                    meztlDB.ReadFeaturesVal();

                    tbGRlist.Text += " " + gr;
                    //ChartSettings.set = false;
                    UpdateChart(gr);
                    UpdateDataSet();
                    timer.Start();
                }
            }
            
        }

        private void UpdateDataSet() 
        {
            if(meztlDB.dtResults != null)
            {
                lstDataSet.Items.Clear();
                for (int i = meztlDB.dtResults.Rows.Count-1; i >=0 ; i--)
                {
                    DataRow row = meztlDB.dtResults.Rows[i];
                    ListViewItem listViewItem = new ListViewItem();
                    long bitRunId = (long)row["BitRunID"];
                    string tag = row["TagName"].ToString();
                    var bitrun = meztlDB.BitRuns.FirstOrDefault(o => o.Id == bitRunId);
                    double scale, off, gtb;
                    if (tag == "TAzm" || tag == "Inc")
                    {
                        scale = 0;
                        off = 0;
                        gtb = bitrun.SurveyToBit;
                    }
                    else
                    {
                        scale = bitrun.features.FirstOrDefault(o => o.FeatureName == tag).GRScale;
                        off = bitrun.features.FirstOrDefault(o => o.FeatureName == tag).GROffset;
                        if (tag == "Gama")
                        {
                            gtb = bitrun.GammaToBit;
                        }
                        else
                        {
                            gtb = off;
                        }
                        
                    }

                    listViewItem.Content = new
                    {
                        BitRun = bitrun.No,
                        MeasureDepth = (float)row["MeasDepthValue"],
                        TagName = tag,
                        OrignalVal = row["ValueNum"],
                        Timestamp = row["TDate"],
                        Scale = scale,
                        Offset = off,
                        GammaToBit = gtb,
                        ResultVal = row["ResultValue"]
                    };

                    listViewItem.HorizontalContentAlignment = HorizontalAlignment.Center;
                    lstDataSet.Items.Add(listViewItem);                  
                }

                lstDataSet.SelectedIndex = lstDataSet.Items.Count - 1;
                lstDataSet.ScrollIntoView(lstDataSet.SelectedItem);
            }
        }

        private void UpdateChart(string addGr = null)
        {
            if (GRList.Count < 1)
                return;
            
            Curve.Series.Clear();

            if ((GRList.Count == 1) && (addGr != null))
            {
                Curve.MaxDepth = 0;
                Curve.MinDepth = 50000;
            }
            
            var data = meztlDB.GetDataSet();

            foreach (string gr in GRList)
            {
                var list = data.GrList.Where(o => o.GrName == gr).FirstOrDefault().grList;
                list.Sort((x, y) => x.depth.CompareTo(y.depth));

                double floor = (int)list.Min(x => x.depth);
                double Ceiling = list.Max(x => x.depth);

                if (addGr != null && gr == addGr)
                {
                    SerieSettings settings = new SerieSettings();
                    settings.grName = gr;
                    settings.stroke = Series.DefaultStroke.serieStrokes[GRList.Count - 1];
                    settings.minVal = list.Min(x => x.val);
                    settings.maxVal = list.Max(x => x.val);

                    if ((int)(settings.minVal * 10) % 10 > 0)
                        settings.minVal = (int)(settings.minVal * 10) / 10.0;
                    if ((int)(settings.maxVal * 10) % 10 > 0)
                        settings.maxVal = (int)(settings.maxVal * 10) / 10.0;

                    ChartSettings.serieSettings.Add(settings);

                }
                SerieSettings s = ChartSettings.serieSettings.Where(o => o.grName == gr).FirstOrDefault();
                Series series = new Series(gr);
                series.Stroke = s.stroke;
                series.StrokeThickness = 2;
                series.MinVal=s.minVal;
                series.MaxVal=s.maxVal;

                if (ChartSettings.set)
                {
                    floor = ChartSettings.minDepth;
                }

                if (floor < Curve.MinDepth)
                {
                    Curve.MinDepth = floor;
                    ChartSettings.minDepth = floor;
                }
                    
                if (Ceiling > Curve.MaxDepth)
                    Curve.MaxDepth = Ceiling;

                int cnt = (int)((Ceiling - floor) * 10) + 1;

                double[] ruler = new double[cnt];
                ruler[0] = floor;
                for (int i = 1; i < cnt; i++)
                {
                    ruler[i] = ruler[i - 1] + 0.1;
                }
                Util.Interpolate(list.Select(o => o.depth).ToArray(), list.Select(o => o.val).ToArray(),ref ruler,out double[] gamaVal, smooth);

                for (int i = 0; i < cnt; i++)
                {
                    series.AddValue(ruler[i], gamaVal[i]);
                }
               
                Curve.Series.Add(series);
                
            }

            if (addGr == null)
            {
                Curve.Refresh();
            }
            else
            {
                if (GRList.Count > 1)
                {
                    Curve.Refresh();

                    cbSector.IsEnabled = true;
                    if (GRList.Count < 4)
                        putSectors();
                    else
                        putSectors(4);
                }
                else
                {
                    Curve.UpdateChart();                  
                }
            }
            
        }

        private void RefreshCurve()
        {
            foreach (var series in Curve.Series)
            {
                SerieSettings s = ChartSettings.serieSettings.Where(o => o.grName == series.Title).FirstOrDefault();
                
                series.Stroke = s.stroke;
                series.StrokeThickness = 2;
                series.MinVal = s.minVal;
                series.MaxVal = s.maxVal;
            }
            
            Curve.MinDepth = ChartSettings.minDepth;
            Curve.Refresh();
        }

        private void btnSeriesConfig_Click(object sender, RoutedEventArgs e)
        {
            ChartSettingWindow chartSettingWindow = new ChartSettingWindow();            
            chartSettingWindow.Owner= this;
            chartSettingWindow.WindowStartupLocation = WindowStartupLocation.CenterOwner;
            bool? ret = chartSettingWindow.ShowDialog();          
            if (ret == true)
            {
                RefreshCurve();
            }
        }

        private void btnImgExport_Click(object sender, RoutedEventArgs e)
        {
            if (GRList.Count < 1)
            {
                return ;
            }

            string filepath = "";
            SaveFileDialog openFileDialog = new SaveFileDialog();
            openFileDialog.Title = "保存图片";
            openFileDialog.AddExtension = true;
            openFileDialog.Filter = "png图片(*.png)|*.png";
            if (openFileDialog.ShowDialog() == true)
            {
                filepath= openFileDialog.FileName;
            }
            else
            {
                return;
            }

            try
            {
                var bit = Curve.ExportImg(wellName);
                FileStream fileStream = new FileStream(filepath, FileMode.Create);
                PngBitmapEncoder encoder = new PngBitmapEncoder();
                encoder.Frames.Add(BitmapFrame.Create(bit));
                encoder.Save(fileStream);
                fileStream.Flush();
                fileStream.Close();
                GC.Collect();
                MessageBox.Show("保存完成！");
                
            }

            catch (Exception ex)
            {
                MessageBox.Show("图片保存失败！\n"+ex.Message);                
            }
            

        }

        private void btnGammaImgOK_Click(object sender, RoutedEventArgs e)
        {
            string sector = cbSector.Text.ToString();
            string[] grs;
            if (sector == "2")
            {
                grs = new string[2];
                grs[0] = cbUp.Text.ToString();
                grs[1] = cbDown.Text.ToString();
                if (grs[0] == grs[1])
                {
                    MessageBox.Show("不能选择相同的标签！");
                    return;
                }
                Curve.SetGammaImg(grs);
                RefreshCurve();
            }
            else if (sector == "4")
            {                
                grs = new string[4];
                grs[0] = cbUp.Text.ToString();
                grs[1] = cbDown.Text.ToString();
                grs[2] = cbLeft.Text.ToString();
                grs[3] = cbRight.Text.ToString();
                
                if (grs[0] == grs[1] || grs[0] == grs[2] || grs[0] == grs[3] || grs[1] == grs[2] || grs[1] == grs[3] || grs[2] == grs[3])
                {
                    MessageBox.Show("不能选择相同的标签！");
                    return;
                }

                Curve.SetGammaImg(grs);
                RefreshCurve();
            }
            else
            {
                return;
            }
        }

        private void cbSector_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ComboBoxItem comboBoxItem=cbSector.SelectedItem as ComboBoxItem;
            string sector = comboBoxItem.Content.ToString();
            if (sector == "2")
            {

            }
            else if (sector == "4")
            {
                if (GRList.Count < 4)
                {
                    MessageBox.Show("请先添加至少4个Gamma值！");
                    cbSector.SelectedIndex = 1;
                    return;
                }
            }
            else
            {

            }
        }

        private void ckSmooth_Click(object sender, RoutedEventArgs e)
        {
            smooth = ckSmooth.IsChecked.Value;
            //System.Diagnostics.Debug.WriteLine(smooth);

            UserSettings.Instance.smooth = smooth;

            UpdateChart();
        }

        private void btnSaveJob_Click(object sender, RoutedEventArgs e)
        {
            string filepath = "";
            SaveFileDialog openFileDialog = new SaveFileDialog();
            openFileDialog.Title = "保存mezintel数据";
            openFileDialog.AddExtension = true;
            openFileDialog.Filter = "XLSX(*.xlsx)|*.xlsx";
            if (openFileDialog.ShowDialog() == true)
            {
                filepath = openFileDialog.FileName;
            }
            else
            {
                return;
            }

            meztlDB.SaveJobData(filepath);
            
        }

        private void Window_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            UserSettings.SaveXML("config.xml");
        }

        private void tbJobID_TextChanged(object sender, TextChangedEventArgs e)
        {
            TextBox textBox = sender as TextBox;
            string job = tbJobID.Text.Trim();
            if (job.Length < 4)
            {
                return;
            }

            try
            {
                jobId = long.Parse(job);
                meztlDB = new MeztlDB(jobId, true);

                //meztlDB = new MeztlDB(jobId, ckSurvey.IsChecked.Value);
            }
            catch (Exception)
            {
                return;
            }

            if (meztlDB.WellName != null)
            {
                lblStatus.Fill = Brushes.Lime;
                tbWell.Text = meztlDB.WellName;
                if (string.IsNullOrEmpty(tbWellNm.Text.Trim()))
                    tbWellNm.Text = meztlDB.WellName;
                wellName = tbWellNm.Text.Trim();

                cbRuns.Items.Clear();
                cbRuns.Items.Add("All");
                foreach (var run in meztlDB.BitRuns)
                {
                    cbRuns.Items.Add(run.No);
                }
                cbRuns.SelectedIndex = cbRuns.Items.Count - 1;

                //btnDataTrans.IsEnabled = true;
                //btnNetListen.IsEnabled = true;
                btnSaveJob.IsEnabled = true;

            }
        }
    }
}
